<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 测试工具 - 精简版</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; margin: 0 auto; background: white;
            border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 20px; text-align: center; border-radius: 12px 12px 0 0;
        }
        .header h1 { margin: 0; font-size: 2em; font-weight: 300; }
        .main-content { padding: 20px; }
        
        /* 账号选择器 */
        .account-selector {
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
        }
        .account-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .account-selector select { 
            width: 100%; padding: 10px; border: 1px solid #007bff; 
            border-radius: 4px; margin-bottom: 10px;
        }
        .quick-buttons { display: flex; gap: 8px; flex-wrap: wrap; }
        .quick-buttons .btn { flex: 1; min-width: 120px; }
        
        /* 后台用户选择器 */
        .backend-user-selector {
            background: linear-gradient(135deg, #f0f8ff 0%, #e8f5e8 100%);
            border: 2px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 20px;
            display: none;
        }
        .backend-user-selector h4 { margin: 0 0 10px 0; color: #0056b3; }
        .user-info { background: white; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; margin-bottom: 10px; }
        
        /* 按钮样式 */
        .btn {
            padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;
            font-size: 14px; font-weight: 500; transition: all 0.3s ease; margin: 4px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        /* 测试卡片 */
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .test-card {
            background: white; border: 1px solid #e9ecef; border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-card-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e9ecef; }
        .test-title { margin: 0; color: #495057; font-size: 1.1em; }
        .test-type { 
            display: inline-block; background: #007bff; color: white; 
            padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-top: 5px;
        }
        .test-card-body { padding: 15px; }
        
        /* 测试结果 */
        .test-result { margin-top: 15px; padding: 10px; border-radius: 4px; border-left: 4px solid #6c757d; }
        .result-success { background: #d4edda; border-left-color: #28a745; }
        .result-error { background: #f8d7da; border-left-color: #dc3545; }
        .result-pending { background: #fff3cd; border-left-color: #ffc107; }
        
        /* 状态指示器 */
        .auth-status {
            display: inline-block; padding: 4px 8px; border-radius: 4px;
            font-size: 0.9em; font-weight: 500;
        }
        .auth-success { background: #d4edda; color: #155724; }
        .auth-pending { background: #fff3cd; color: #856404; }
        .auth-failed { background: #f8d7da; color: #721c24; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { margin: 10px; }
            .main-content { padding: 15px; }
            .test-grid { grid-template-columns: 1fr; }
            .quick-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 GoMyHire API 测试工具</h1>
            <p>精简版 - 专注核心功能</p>
        </div>
        
        <div class="main-content">
            <!-- 邮箱账号选择器 -->
            <div class="account-selector">
                <h4>📧 邮箱账号选择</h4>
                <select id="accountSelector" onchange="switchAccount()">
                    <option value="">选择邮箱账号...</option>
                </select>
                <div class="quick-buttons">
                    <button type="button" class="btn btn-primary" onclick="switchToAccount('general')">
                        📧 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-success" onclick="switchToAccount('jcy')">
                        👤 <EMAIL>
                    </button>
                    <button type="button" class="btn btn-warning" onclick="testAllAccounts()">
                        🧪 测试所有邮箱
                    </button>
                </div>
                <div class="user-info">
                    <span id="authStatus" class="auth-status auth-pending">正在初始化...</span>
                    <div id="currentAccountInfo" style="margin-top: 5px; font-size: 0.9em;">未选择账号</div>
                </div>
            </div>

            <!-- 后台用户选择器 -->
            <div id="backendUserSelector" class="backend-user-selector">
                <h4>👤 后台用户选择</h4>
                <div id="backendUserInfo" class="user-info">正在加载后台用户列表...</div>
                <select id="backendUserSelect" onchange="selectBackendUser()">
                    <option value="">选择后台用户...</option>
                </select>
                <div id="selectedBackendUserInfo" class="user-info">未选择后台用户</div>
            </div>

            <!-- 测试控制 -->
            <div style="margin-bottom: 20px;">
                <button class="btn btn-success" onclick="runAllOrderTests()">🚀 运行所有测试</button>
                <button class="btn btn-warning" onclick="clearAllResults()">🧹 清除结果</button>
            </div>

            <!-- 测试用例网格 -->
            <div id="orderTestGrid" class="test-grid">
                <!-- 动态生成测试卡片 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE_URL = 'https://gomyhire.com.my/api';
        let authToken = null;
        let currentAccount = null;
        let availableBackendUsers = [];
        let selectedBackendUser = null;
        let orderTestStats = { total: 0, success: 0, failed: 0 };

        // 邮箱账号配置
        const realLoginAccounts = [
            { id: 'general', email: '<EMAIL>', password: 'Gomyhire@123456', isDefault: true },
            { id: 'jcy', email: '<EMAIL>', password: 'Gomyhire@123456', isDefault: false }
        ];

        // 精简的测试用例 - 只保留核心场景
        const orderTestCases = [
            {
                name: '接机服务 - 基础',
                type: 'pickup',
                description: '标准机场接机服务',
                data: {
                    sub_category_id: 2, car_type_id: 5, incharge_by_backend_user_id: 1,
                    ota_reference_number: 'PICKUP_' + Date.now(),
                    customer_name: '张三', customer_contact: '+***********', customer_email: '<EMAIL>',
                    pickup: 'KLIA Terminal 1', destination: 'Hotel Sentral',
                    date: '2025-01-15', time: '15:30', passenger_number: 2, luggage_number: 2,
                    driving_region_id: 1, languages_id_array: [2, 4],
                    extra_requirement: 'TESTING - API测试订单，请勿处理'
                }
            },
            {
                name: '送机服务 - 基础',
                type: 'dropoff',
                description: '标准机场送机服务',
                data: {
                    sub_category_id: 3, car_type_id: 15, incharge_by_backend_user_id: 310,
                    ota_reference_number: 'DROPOFF_' + Date.now(),
                    customer_name: '李四', customer_contact: '+60198765432', customer_email: '<EMAIL>',
                    pickup: 'Hotel Sentral', destination: 'KLIA2',
                    date: '2025-01-16', time: '07:00', passenger_number: 4, luggage_number: 4,
                    driving_region_id: 1, languages_id_array: [2, 3],
                    extra_requirement: 'TESTING - API测试订单，请勿处理'
                }
            },
            {
                name: '包车服务 - 基础',
                type: 'charter',
                description: '标准包车服务',
                data: {
                    sub_category_id: 4, car_type_id: 20, incharge_by_backend_user_id: 311,
                    ota_reference_number: 'CHARTER_' + Date.now(),
                    customer_name: '王五', customer_contact: '+60187654321', customer_email: '<EMAIL>',
                    pickup: 'Twin Towers', destination: 'Genting',
                    date: '2025-01-17', time: '08:00', passenger_number: 6, luggage_number: 6,
                    driving_region_id: 1, languages_id_array: [2, 4],
                    extra_requirement: 'TESTING - API测试订单，请勿处理'
                }
            },
            {
                name: 'VIP服务 - 豪华',
                type: 'vip',
                description: 'VIP豪华车服务',
                data: {
                    sub_category_id: 2, car_type_id: 32, incharge_by_backend_user_id: 310,
                    ota_reference_number: 'VIP_' + Date.now(),
                    customer_name: 'VIP客户', customer_contact: '+60187654321', customer_email: '<EMAIL>',
                    pickup: 'KLIA First Class Lounge', destination: 'Four Seasons Hotel KL',
                    date: '2025-01-18', time: '16:45', passenger_number: 4, luggage_number: 6,
                    driving_region_id: 1, languages_id_array: [2, 4],
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - VIP服务'
                }
            },
            {
                name: '最小字段测试',
                type: 'minimal',
                description: '最小必需字段测试',
                data: {
                    sub_category_id: 2, car_type_id: 5, incharge_by_backend_user_id: 1,
                    ota_reference_number: 'MINIMAL_' + Date.now(),
                    customer_name: 'API测试', customer_contact: '+***********', customer_email: '<EMAIL>',
                    pickup: 'Test Pickup', destination: 'Test Destination',
                    date: '2025-01-15', time: '14:00', passenger_number: 1, luggage_number: 1,
                    driving_region_id: 1, languages_id_array: [2],
                    extra_requirement: 'TESTING - API测试订单，请勿处理'
                }
            }
        ];

        // 工具函数
        function safeGetElement(id) {
            return document.getElementById(id);
        }

        function safeSetTextContent(id, text) {
            const element = safeGetElement(id);
            if (element) element.textContent = text;
        }

        function safeExecute(func, ...args) {
            try {
                return func(...args);
            } catch (error) {
                console.error('执行函数时出错:', error);
                alert('操作失败: ' + error.message);
            }
        }

        // 准备订单数据，同步当前认证信息
        function prepareOrderData(originalData) {
            const orderData = JSON.parse(JSON.stringify(originalData));
            if (selectedBackendUser && selectedBackendUser.id) {
                orderData.incharge_by_backend_user_id = selectedBackendUser.id;
            }
            return orderData;
        }

        // 验证认证状态
        function validateAuthenticationStatus() {
            const status = {
                hasToken: !!authToken,
                hasAccount: !!currentAccount,
                message: ''
            };

            if (!status.hasToken) {
                status.message = '未找到认证token，请先登录邮箱账号';
            } else {
                status.message = '认证状态正常';
            }

            return status;
        }

        // 运行单个订单测试
        async function runSingleOrderTest(index) {
            if (index < 0 || index >= orderTestCases.length) {
                console.error('无效的测试用例索引:', index);
                return;
            }

            const testCase = orderTestCases[index];
            const resultContainer = safeGetElement(`orderResult${index}`);

            if (!resultContainer) {
                console.error('找不到结果容器:', `orderResult${index}`);
                return;
            }

            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '🔄 测试进行中...';

            try {
                // 验证认证状态
                const authStatus = validateAuthenticationStatus();
                if (!authStatus.hasToken) {
                    throw new Error(`认证失败: ${authStatus.message}`);
                }

                // 准备订单数据
                const orderData = prepareOrderData(testCase.data);
                const startTime = Date.now();

                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(orderData)
                });

                const responseTime = Date.now() - startTime;
                const responseText = await response.text();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无效的JSON响应: ${parseError.message}`);
                }

                if (result.status === true || result.status === 'true') {
                    orderTestStats.success++;
                    orderTestStats.total++;

                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>测试成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                } else {
                    throw new Error(result.message || result.error || '订单创建失败');
                }

            } catch (error) {
                orderTestStats.failed++;
                orderTestStats.total++;

                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>测试失败</strong><br>
                    错误: ${error.message}<br>
                    <small>测试用例: ${testCase.name}</small>
                `;

                console.error(`订单测试失败: ${testCase.name}`, error);
            }
        }

        // 运行所有订单测试
        async function runAllOrderTests() {
            orderTestStats = { total: 0, success: 0, failed: 0 };

            for (let i = 0; i < orderTestCases.length; i++) {
                await runSingleOrderTest(i);
                if (i < orderTestCases.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            console.log('所有测试完成', orderTestStats);
        }

        // 清除所有测试结果
        function clearAllResults() {
            orderTestStats = { total: 0, success: 0, failed: 0 };
            orderTestCases.forEach((_, index) => {
                const resultContainer = safeGetElement(`orderResult${index}`);
                if (resultContainer) {
                    resultContainer.style.display = 'none';
                    resultContainer.className = 'test-result';
                    resultContainer.innerHTML = '';
                }
            });
        }

        // 切换到指定账号
        async function switchToAccount(accountId) {
            const account = realLoginAccounts.find(acc => acc.id === accountId);
            if (!account) {
                console.error('账号不存在:', accountId);
                return;
            }

            // 更新选择器
            const selector = safeGetElement('accountSelector');
            if (selector) selector.value = accountId;

            // 清除之前的数据
            authToken = null;
            currentAccount = null;
            availableBackendUsers = [];
            selectedBackendUser = null;

            // 隐藏后台用户选择器
            const backendUserSelector = safeGetElement('backendUserSelector');
            if (backendUserSelector) backendUserSelector.style.display = 'none';

            // 开始认证
            updateAuthStatusIndicator(false, `正在切换到 ${account.email}...`);
            updateCurrentAccountInfo('切换中...');

            const success = await authenticateAccount(account);
            if (success) {
                currentAccount = account;
                updateCurrentAccountInfo(`当前邮箱: ${account.email}`);
                await loadBackendUsers();
            } else {
                updateCurrentAccountInfo('认证失败');
            }
        }

        // 从选择器切换账号
        async function switchAccount() {
            const selector = safeGetElement('accountSelector');
            if (!selector || !selector.value) return;
            await switchToAccount(selector.value);
        }

        // 认证指定账号
        async function authenticateAccount(account) {
            try {
                const loginResponse = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        'email': account.email,
                        'password': account.password
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.status && loginData.token) {
                        const fullToken = loginData.token;
                        authToken = fullToken.includes('|') ? fullToken.split('|')[1] : fullToken;
                        updateAuthStatusIndicator(true, `✅ ${account.email} 认证成功`);
                        return true;
                    }
                }

                updateAuthStatusIndicator(false, `❌ ${account.email} 认证失败`);
                return false;

            } catch (error) {
                updateAuthStatusIndicator(false, `❌ ${account.email} 认证失败：网络错误`);
                return false;
            }
        }

        // 加载后台用户列表
        async function loadBackendUsers() {
            if (!authToken) return [];

            try {
                const response = await fetch(`${API_BASE_URL}/backend_users?search=`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableBackendUsers = data.data;
                        updateBackendUserSelector();
                        if (availableBackendUsers.length > 0) {
                            selectBackendUser(availableBackendUsers[0].id);
                        }
                        return availableBackendUsers;
                    }
                }
                return [];
            } catch (error) {
                console.error('获取后台用户时出错:', error);
                return [];
            }
        }

        // 更新后台用户选择器
        function updateBackendUserSelector() {
            const selector = safeGetElement('backendUserSelect');
            if (!selector) return;

            selector.innerHTML = '<option value="">选择后台用户...</option>';
            availableBackendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (ID: ${user.id}) - ${user.role || '未知角色'}`;
                selector.appendChild(option);
            });

            const selectorContainer = safeGetElement('backendUserSelector');
            if (selectorContainer) selectorContainer.style.display = 'block';

            updateBackendUserInfo(`找到 ${availableBackendUsers.length} 个可用后台用户`);
        }

        // 选择后台用户
        function selectBackendUser(userId = null) {
            if (!userId) {
                const selector = safeGetElement('backendUserSelect');
                if (!selector || !selector.value) return;
                userId = parseInt(selector.value);
            }

            const user = availableBackendUsers.find(u => u.id === userId);
            if (!user) return;

            selectedBackendUser = user;
            const selector = safeGetElement('backendUserSelect');
            if (selector) selector.value = userId;

            updateSelectedBackendUserInfo(user);
        }

        // 更新状态显示
        function updateAuthStatusIndicator(isAuthenticated, customMessage = null) {
            const indicator = safeGetElement('authStatus');
            if (indicator) {
                if (isAuthenticated) {
                    indicator.className = 'auth-status auth-success';
                    indicator.textContent = customMessage || '✅ 认证已完成';
                } else {
                    indicator.className = 'auth-status auth-pending';
                    indicator.textContent = customMessage || '正在认证中...';
                }
            }
        }

        function updateCurrentAccountInfo(message) {
            const infoElement = safeGetElement('currentAccountInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateBackendUserInfo(message) {
            const infoElement = safeGetElement('backendUserInfo');
            if (infoElement) infoElement.textContent = message;
        }

        function updateSelectedBackendUserInfo(user) {
            const infoElement = safeGetElement('selectedBackendUserInfo');
            if (infoElement && user) {
                infoElement.innerHTML = `
                    <strong>当前选中:</strong> ${user.name}<br>
                    <strong>用户ID:</strong> ${user.id}<br>
                    <strong>角色:</strong> ${user.role || '未指定'}
                `;
            } else if (infoElement) {
                infoElement.textContent = '未选择后台用户';
            }
        }

        // 初始化账号选择器
        function initializeAccountSelector() {
            const selector = safeGetElement('accountSelector');
            if (!selector) return;

            selector.innerHTML = '<option value="">选择邮箱账号...</option>';
            realLoginAccounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;
                option.textContent = account.email;
                if (account.isDefault) option.selected = true;
                selector.appendChild(option);
            });
        }

        // 渲染测试卡片
        function renderOrderTestCards() {
            const container = safeGetElement('orderTestGrid');
            if (!container) return;

            container.innerHTML = '';

            orderTestCases.forEach((testCase, index) => {
                const card = document.createElement('div');
                card.className = 'test-card';

                const typeColor = testCase.type === 'pickup' ? '#28a745' :
                                testCase.type === 'dropoff' ? '#007bff' :
                                testCase.type === 'charter' ? '#ffc107' :
                                testCase.type === 'vip' ? '#dc3545' : '#6c757d';

                card.innerHTML = `
                    <div class="test-card-header">
                        <h4 class="test-title">${testCase.name}</h4>
                        <span class="test-type" style="background: ${typeColor};">${testCase.type}</span>
                    </div>
                    <div class="test-card-body">
                        <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">${testCase.description}</p>
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 8px 0; font-size: 0.85em;">
                            <div style="color: #28a745;">📍 接机: ${testCase.data.pickup}</div>
                            <div style="color: #dc3545;">🎯 送达: ${testCase.data.destination}</div>
                        </div>
                        <button class="btn btn-primary" onclick="safeExecute(runSingleOrderTest, ${index})">
                            测试此订单
                        </button>
                        <div id="orderResult${index}" class="test-result" style="display: none;"></div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 测试所有邮箱账号
        async function testAllAccounts() {
            console.log('开始测试所有邮箱账号...');
            for (const account of realLoginAccounts) {
                console.log(`测试邮箱: ${account.email}`);
                const success = await authenticateAccount(account);
                if (success) {
                    currentAccount = account;
                    await loadBackendUsers();
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            console.log('所有邮箱账号测试完成');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('GoMyHire API测试工具初始化 - 精简版');
            try {
                renderOrderTestCards();
                initializeAccountSelector();

                updateAuthStatusIndicator(false, '正在初始化...');
                updateCurrentAccountInfo('正在加载默认邮箱账号...');

                const defaultAccount = realLoginAccounts.find(acc => acc.isDefault);
                if (defaultAccount) {
                    await switchToAccount(defaultAccount.id);
                } else {
                    updateAuthStatusIndicator(false, '请选择邮箱账号');
                    updateCurrentAccountInfo('请从上方选择邮箱账号');
                }

                console.log('测试工具初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                updateAuthStatusIndicator(false, '❌ 初始化失败');
                updateCurrentAccountInfo('初始化失败');
            }
        });
    </script>
</body>
</html>
