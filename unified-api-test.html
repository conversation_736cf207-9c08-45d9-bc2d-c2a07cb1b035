<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 认证测试工具</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1600px; 
            margin: 0 auto; 
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 30px; 
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2.5em; font-weight: 300; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        
        .main-content { padding: 30px; }
        
        .section {
            background: #f8f9fa;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }
        .section-header {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 1.1em;
        }
        .section-body { padding: 20px; }
        
        .auth-form { 
            display: flex; 
            gap: 15px; 
            align-items: end; 
            flex-wrap: wrap; 
            margin-bottom: 20px;
        }
        .form-group { 
            flex: 1; 
            min-width: 200px; 
        }
        .form-group label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: 500; 
        }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-1px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; transform: translateY(-1px); }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; transform: translateY(-1px); }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; transform: translateY(-1px); }
        .btn:disabled { 
            opacity: 0.6; 
            cursor: not-allowed; 
            transform: none !important; 
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stat-number { font-size: 1.8em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.8em; opacity: 0.9; }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .test-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-card-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .test-title { margin: 0; color: #495057; font-size: 1.1em; }
        .test-type { 
            display: inline-block; 
            background: #007bff; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 12px; 
            font-size: 0.8em; 
            margin-top: 5px;
        }
        .test-card-body { padding: 15px; }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #6c757d;
        }
        .result-success { background: #d4edda; border-left-color: #28a745; }
        .result-error { background: #f8d7da; border-left-color: #dc3545; }
        .result-pending { background: #fff3cd; border-left-color: #ffc107; }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        
        .data-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .data-item {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .data-item:last-child { border-bottom: none; }
        
        .error-boundary {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .container { margin: 10px; }
            .main-content { padding: 20px; }
            .auth-form { flex-direction: column; }
            .test-grid { grid-template-columns: 1fr; }
        }



        /* 测试统计样式优化 */
        .test-stats {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-stats h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .stat-label {
            display: block;
            font-size: 0.85em;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .stat-value {
            display: block;
            font-size: 1.5em;
            font-weight: bold;
            color: #495057;
        }

        .stat-value.success {
            color: #28a745;
        }

        .stat-value.error {
            color: #dc3545;
        }

        /* 批量测试控制按钮 */
        .batch-test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .batch-test-controls .btn {
            flex: 1;
            min-width: 140px;
        }

        .btn-outline {
            background: transparent;
            color: #6c757d;
            border: 1px solid #6c757d;
        }

        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }

        /* 多账号测试相关样式 */
        .account-test-results {
            margin: 15px 0;
        }

        .account-test-results table {
            font-size: 0.9em;
        }

        .account-test-results th {
            background: #f8f9fa !important;
            font-weight: 600;
        }

        .account-test-results td {
            vertical-align: middle;
        }

        .auth-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .auth-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
        }

        .auth-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            animation: pulse 2s infinite;
        }

        .auth-failed {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
        }

        /* 账号选择器优化样式 */
        .account-selector-enhanced {
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border: 2px solid #b3d9ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
        }

        .account-selector-enhanced h4 {
            margin: 0 0 15px 0;
            color: #0056b3;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .account-selector-enhanced select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #007bff;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .account-selector-enhanced select:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }

        .account-quick-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .account-quick-buttons .btn {
            flex: 1;
            min-width: 140px;
            padding: 8px 12px;
            font-size: 0.9em;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .account-quick-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .account-status-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .account-status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .account-status-icon.connected {
            background: #28a745;
            animation: none;
        }

        .account-status-icon.connecting {
            background: #ffc107;
        }

        .account-status-icon.disconnected {
            background: #dc3545;
            animation: none;
        }

        /* 后台用户选择器优化样式 */
        .backend-user-selector-enhanced {
            background: linear-gradient(135deg, #f0f8ff 0%, #e8f5e8 100%);
            border: 2px solid #b3d9ff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
        }

        .backend-user-selector-enhanced h4 {
            margin: 0 0 15px 0;
            color: #0056b3;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .backend-user-info-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #666;
        }

        .backend-user-select-enhanced {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #28a745;
            border-radius: 8px;
            font-size: 1em;
            background: white;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .backend-user-select-enhanced:focus {
            outline: none;
            border-color: #1e7e34;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
        }

        .selected-user-info-enhanced {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            font-size: 0.9em;
        }

        .selected-user-info-enhanced .user-name {
            font-weight: 600;
            color: #155724;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .selected-user-info-enhanced .user-details {
            color: #155724;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .selected-user-info-enhanced .user-detail-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 动画效果 */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* 地址模板样式 */
        .address-templates {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .address-templates h4 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1em;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .template-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .template-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
            transform: translateY(-1px);
        }

        .template-item.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }

        .template-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .template-addresses {
            font-size: 0.85em;
            color: #6c757d;
            line-height: 1.4;
        }

        .template-addresses .pickup {
            color: #28a745;
            font-weight: 500;
        }

        .template-addresses .destination {
            color: #dc3545;
            font-weight: 500;
        }

        .address-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .address-input-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
            min-width: 200px;
        }

        .address-input-group label {
            font-weight: 500;
            color: #495057;
            font-size: 0.9em;
        }

        .address-input-group input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .address-input-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GoMyHire API 认证测试工具</h1>
            <p>自动登录认证，使用Live环境API进行完整的订单创建测试</p>
        </div>
        
        <div class="main-content">
            <!-- 无认证模式说明 -->
            <div class="section">
                <div class="section-header">
                    � 无认证模式说明
                </div>
                <div class="section-body">
                    <div style="background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                        <h5 style="color: #155724; margin: 0 0 10px 0;">✅ 无认证测试模式特点</h5>
                        <ul style="margin: 0; color: #155724; font-size: 14px;">
                            <li><strong>无需登录</strong>: 跳过邮箱密码认证步骤，直接开始测试</li>
                            <li><strong>简化流程</strong>: 一键运行20+个完整测试用例</li>
                            <li><strong>地址模板</strong>: 支持快速切换真实地址进行测试</li>
                            <li><strong>批量测试</strong>: 支持全部测试、基础测试、高级测试</li>
                            <li><strong>认证模式</strong>: 自动登录获取Bearer token进行API调用</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 订单创建测试 -->
            <div class="section">
                <div class="section-header">
                    🎯 第三步：订单创建测试 (已优化)
                </div>
                <div class="section-body">
                    <div style="background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                        <h5 style="color: #155724; margin: 0 0 10px 0;">✅ 最新优化内容</h5>
                        <ul style="margin: 0; color: #155724; font-size: 14px;">
                            <li><strong>高级功能修正</strong>: tour_guide改为布尔值 (true/false)</li>
                            <li><strong>边界测试优化</strong>: 大型团体乘客数降至35人（符合43座车型限制）</li>
                            <li><strong>新增测试用例</strong>: 中型团体测试 + 高级包车完整测试</li>
                            <li><strong>预期成功率</strong>: 从66.7%提升至85%以上</li>
                        </ul>
                </div>
                



                    <!-- 邮箱账号选择器 - 优化版 -->
                    <div class="account-selector-enhanced slide-in">
                        <h4>📧 邮箱账号选择</h4>
                        <select id="accountSelector" onchange="switchAccount()" title="选择邮箱账号">
                            <option value="">选择邮箱账号...</option>
                        </select>
                        <div class="account-quick-buttons">
                            <button type="button" class="btn btn-primary" onclick="switchToAccount('general')">
                                📧 <EMAIL>
                            </button>
                            <button type="button" class="btn btn-info" onclick="switchToAccount('jcy')">
                                👤 <EMAIL>
                            </button>
                            <button type="button" class="btn btn-success" onclick="switchToAccount('sq')">
                                🔧 <EMAIL>
                            </button>
                            <button type="button" class="btn btn-warning" onclick="testAllAccounts()">
                                🧪 测试所有邮箱
                            </button>
                        </div>
                        <div class="account-status-display">
                            <div id="accountStatusIcon" class="account-status-icon connecting"></div>
                            <div id="currentAccountInfo">未选择账号</div>
                        </div>
                    </div>

                    <!-- 后台用户选择器 - 优化版 -->
                    <div id="backendUserSelector" class="backend-user-selector-enhanced slide-in" style="display: none;">
                        <h4>👤 后台用户选择</h4>
                        <div class="backend-user-info-display" id="backendUserInfo">
                            正在加载后台用户列表...
                        </div>
                        <select id="backendUserSelect" class="backend-user-select-enhanced" onchange="selectBackendUser()" title="选择后台用户">
                            <option value="">选择后台用户...</option>
                        </select>
                        <div id="selectedBackendUserInfo" class="selected-user-info-enhanced">
                            <div class="user-name">未选择后台用户</div>
                            <div class="user-details">
                                <div class="user-detail-item">
                                    <span>🆔</span>
                                    <span>请先选择用户</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 认证状态指示器 -->
                    <div id="authStatusIndicator" class="test-stats" style="background: #fff3cd; border-color: #ffeaa7;">
                        <h4>� 认证状态</h4>
                        <div style="text-align: center; padding: 10px;">
                            <span id="authStatus" class="auth-status auth-pending" style="color: #856404; font-weight: 500;">
                                正在初始化认证...
                            </span>
                            <div id="currentAccountInfo" style="margin-top: 5px; font-size: 0.9em; color: #666;">
                                未选择账号
                            </div>
                        </div>
                    </div>

                    <!-- 测试统计 -->
                    <div class="test-stats">
                        <h4>📊 测试统计</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">总测试数</span>
                                <span class="stat-value" id="totalTests">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">成功</span>
                                <span class="stat-value success" id="successTests">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">失败</span>
                                <span class="stat-value error" id="failedTests">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">成功率</span>
                                <span class="stat-value" id="successRate">0%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 地址模板选择器 -->
                    <div class="address-templates">
                        <h4>📍 地址模板 - 快速应用真实地址</h4>
                        <div class="template-grid" id="addressTemplateGrid">
                            <!-- 动态生成地址模板 -->
                        </div>
                        <div class="address-controls">
                            <div class="address-input-group">
                                <label>自定义接机地址:</label>
                                <input type="text" id="customPickup" placeholder="输入接机地址...">
                            </div>
                            <div class="address-input-group">
                                <label>自定义送达地址:</label>
                                <input type="text" id="customDestination" placeholder="输入送达地址...">
                            </div>
                            <button type="button" class="btn btn-success" onclick="safeExecute(applyCustomAddresses)">
                                应用自定义地址
                            </button>
                            <button type="button" class="btn btn-outline" onclick="safeExecute(resetToDefaultAddresses)">
                                重置默认地址
                            </button>
                        </div>
                    </div>
                    
                    <!-- API诊断工具 -->
                    <div class="api-diagnostics" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                        <h5 style="color: #856404; margin: 0 0 10px 0;">🔧 API诊断工具</h5>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
                            <button type="button" class="btn btn-warning" onclick="safeExecute(runAPIHealthCheck)">
                                检查API健康状态
                            </button>
                            <button type="button" class="btn btn-warning" onclick="safeExecute(testMinimalRequest)">
                                测试最小请求
                            </button>
                            <button type="button" class="btn btn-warning" onclick="safeExecute(testWithAuth)">
                                测试带认证请求
                            </button>
                            <button type="button" class="btn btn-info" onclick="safeExecute(validateAPICompliance)">
                                验证API文档合规性
                            </button>
                        </div>
                        <div id="diagnosticResults" style="display: none; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
                    </div>

                    <!-- 账号测试结果显示区域 -->
                    <div id="accountTestResultsSection" style="display: none; margin-bottom: 20px;">
                        <h4>📊 多账号测试结果对比</h4>
                        <div id="accountTestResults" class="account-test-results">
                            <!-- 动态生成测试结果表格 -->
                        </div>
                    </div>

                    <!-- 批量测试按钮 -->
                    <div class="batch-test-controls">
                        <button type="button" class="btn btn-primary" onclick="safeExecute(runAllOrderTests)">
                            🚀 运行所有测试
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="safeExecute(runBasicOrderTests)">
                            运行基础测试
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="safeExecute(runAdvancedOrderTests)">
                            运行高级测试
                        </button>
                        <button type="button" class="btn btn-outline" onclick="safeExecute(clearAllResults)">
                            🗑️ 清除结果
                        </button>
                    </div>
                    
                    <!-- 订单测试网格 -->
                    <div id="orderTestGrid" class="test-grid">
                        <!-- 动态生成测试卡片 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });

        // 安全执行函数包装器
        function safeExecute(func, ...args) {
            try {
                const result = func.apply(this, args);
                if (result && typeof result.catch === 'function') {
                    result.catch(error => {
                        console.error(`函数 ${func.name} 执行失败:`, error);
                        showError(`操作失败: ${error.message}`);
                    });
                }
                return result;
            } catch (error) {
                console.error(`函数 ${func.name} 执行失败:`, error);
                showError(`操作失败: ${error.message}`);
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-boundary';
            errorDiv.innerHTML = `❌ ${message}`;
            
            // 找到合适的位置插入错误信息
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.insertBefore(errorDiv, mainContent.firstChild);
                
                // 5秒后自动移除
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }
        }

        // 安全的DOM操作
        function safeGetElement(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`元素 ${id} 不存在`);
            }
            return element;
        }

        function safeSetTextContent(id, content) {
            const element = safeGetElement(id);
            if (element) {
                element.textContent = content;
            }
        }

        function safeSetInnerHTML(id, html) {
            const element = safeGetElement(id);
            if (element) {
                element.innerHTML = html;
            }
        }

        // API配置（认证模式）
        const API_BASE_URL = 'https://gomyhire.com.my/api';
        let authToken = null; // 存储认证token
        let currentAccount = null; // 当前登录账号信息

        // 真实登录凭据配置（基于Live环境）
        const realLoginAccounts = [
            {
                id: 'general',
                email: '<EMAIL>',
                password: 'Gomyhire@123456',
                isDefault: true
            },
            {
                id: 'jcy',
                email: '<EMAIL>',
                password: 'Yap123'
            },
            {
                id: 'sq',
                email: '<EMAIL>',
                password: 'NtzPATJgmWU!H'
            },
            {
                id: 'smw',
                email: '<EMAIL>',
                password: 'Sky@114788'
            },
            {
                id: 'support',
                email: '<EMAIL>',
                password: 'Support@123456'
            }
        ];

        // 账号测试结果存储
        let accountTestResults = {};

        // 动态后台用户管理
        let availableBackendUsers = []; // 当前账号可访问的后台用户列表
        let selectedBackendUser = null; // 当前选中的后台用户
        let accountBackendUsersMap = {}; // 存储每个账号对应的后台用户列表
        
        // 系统数据存储
        let systemData = {
            backendUsers: [],
            subCategories: [],
            carTypes: [],
            drivingRegions: [],
            languages: []
        };
        
        // 订单测试统计
        let orderTestStats = {
            total: 0,
            success: 0,
            failed: 0
        };

        // 地址模板数据
        const addressTemplates = [
            {
                id: 'airport_kl',
                name: '机场 ↔ 吉隆坡市中心',
                pickup: 'Kuala Lumpur International Airport (KLIA)',
                destination: 'KLCC - Kuala Lumpur City Centre',
                category: 'airport',
                popular: true
            },
            {
                id: 'airport_klia2',
                name: 'KLIA2 ↔ 双子塔',
                pickup: 'KLIA2 Terminal',
                destination: 'Petronas Twin Towers KLCC',
                category: 'airport',
                popular: true
            },
            {
                id: 'hotel_airport',
                name: '酒店 ↔ 机场',
                pickup: 'Hotel Sentral Kuala Lumpur',
                destination: 'KLIA Terminal 1',
                category: 'airport',
                popular: false
            },
            {
                id: 'city_tour',
                name: '吉隆坡市区游',
                pickup: 'Merdeka Square (Independence Square)',
                destination: 'Batu Caves Temple',
                category: 'tour',
                popular: true
            },
            {
                id: 'genting_trip',
                name: '云顶高原一日游',
                pickup: 'Kuala Lumpur City Center',
                destination: 'Genting Highlands Resort',
                category: 'tour',
                popular: true
            },
            {
                id: 'melaka_tour',
                name: '马六甲历史游',
                pickup: 'KL Sentral Transportation Hub',
                destination: 'Melaka Historic City Center',
                category: 'tour',
                popular: false
            },
            {
                id: 'skymirror_tour',
                name: '天空之镜一日游',
                pickup: 'KL Sentral Station',
                destination: 'Sky Mirror Kuala Selangor',
                category: 'tour',
                popular: true
            },
            {
                id: 'putrajaya_tour',
                name: '布城政府区游览',
                pickup: 'Kuala Lumpur Sentral',
                destination: 'Putrajaya Government Complex',
                category: 'tour',
                popular: false
            },
            {
                id: 'shopping_tour',
                name: '购物中心穿梭',
                pickup: 'Pavilion Kuala Lumpur',
                destination: 'Sunway Pyramid Shopping Mall',
                category: 'shopping',
                popular: false
            },
            {
                id: 'business_trip',
                name: '商务区接送',
                pickup: 'Kuala Lumpur Convention Centre',
                destination: 'Menara KL Tower',
                category: 'business',
                popular: false
            }
        ];

        // 当前选中的地址模板
        let selectedAddressTemplate = null;
        
        // 默认地址备份（用于重置）
        let defaultAddresses = {};

        // 保存默认地址
        function saveDefaultAddresses() {
            defaultAddresses = {};
            orderTestCases.forEach((testCase, index) => {
                defaultAddresses[index] = {
                    pickup: testCase.data.pickup || '',
                    destination: testCase.data.destination || ''
                };
            });
        }

        // 初始化地址模板
        function initializeAddressTemplates() {
            const grid = document.getElementById('addressTemplateGrid');
            if (!grid) return;
            
            grid.innerHTML = '';

            addressTemplates.forEach(template => {
                const templateElement = document.createElement('div');
                templateElement.className = 'template-item';
                templateElement.dataset.templateId = template.id;
                
                templateElement.innerHTML = `
                    <div class="template-title">
                        ${template.popular ? '⭐ ' : ''}${template.name}
                    </div>
                    <div class="template-addresses">
                        <div><span class="pickup">接机:</span> ${template.pickup}</div>
                        <div><span class="destination">送达:</span> ${template.destination}</div>
                    </div>
                `;

                templateElement.addEventListener('click', () => selectAddressTemplate(template.id));
                grid.appendChild(templateElement);
            });

            // 保存默认地址
            saveDefaultAddresses();
        }

        // 选择地址模板
        function selectAddressTemplate(templateId) {
            // 移除之前的选中状态
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 选中当前模板
            const templateElement = document.querySelector(`[data-template-id="${templateId}"]`);
            if (templateElement) {
                templateElement.classList.add('selected');
            }

            // 找到模板数据
            const template = addressTemplates.find(t => t.id === templateId);
            if (template) {
                selectedAddressTemplate = template;
                applyAddressTemplate(template);
                updateOrderTestStatus(`已选择地址模板: ${template.name}`, 'success');
            }
        }

        // 应用地址模板到所有测试用例
        function applyAddressTemplate(template) {
            orderTestCases.forEach(testCase => {
                // 根据测试用例类型智能应用地址
                if (testCase.name.includes('接机') || testCase.type === 'pickup') {
                    testCase.data.pickup = template.pickup;
                    testCase.data.destination = template.destination;
                } else if (testCase.name.includes('送机') || testCase.type === 'dropoff') {
                    testCase.data.pickup = template.destination;
                    testCase.data.destination = template.pickup;
                } else {
                    // 其他类型（包车、游览等）使用原地址
                    testCase.data.pickup = template.pickup;
                    testCase.data.destination = template.destination;
                }
            });

            // 更新测试用例显示
            renderOrderTestCases();
        }

        // 应用自定义地址
        function applyCustomAddresses() {
            const customPickup = document.getElementById('customPickup').value.trim();
            const customDestination = document.getElementById('customDestination').value.trim();

            if (!customPickup && !customDestination) {
                updateOrderTestStatus('请输入至少一个地址', 'error');
                return;
            }

            // 清除模板选择
            selectedAddressTemplate = null;
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 应用自定义地址到所有测试用例
            orderTestCases.forEach(testCase => {
                if (customPickup) {
                    testCase.data.pickup = customPickup;
                }
                if (customDestination) {
                    testCase.data.destination = customDestination;
                }
            });

            // 更新测试用例显示
            renderOrderTestCases();
            
            updateOrderTestStatus(`已应用自定义地址${customPickup ? ` 接机: ${customPickup}` : ''}${customDestination ? ` 送达: ${customDestination}` : ''}`, 'success');
        }

        // 重置到默认地址
        function resetToDefaultAddresses() {
            // 清除模板选择
            selectedAddressTemplate = null;
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 清空自定义输入
            const customPickup = document.getElementById('customPickup');
            const customDestination = document.getElementById('customDestination');
            if (customPickup) customPickup.value = '';
            if (customDestination) customDestination.value = '';

            // 恢复默认地址
            orderTestCases.forEach((testCase, index) => {
                if (defaultAddresses[index]) {
                    testCase.data.pickup = defaultAddresses[index].pickup;
                    testCase.data.destination = defaultAddresses[index].destination;
                }
            });

            // 更新测试用例显示
            renderOrderTestCases();
            
            updateOrderTestStatus('已重置为默认地址', 'success');
        }

        // 订单测试用例 - 完整覆盖所有类型
        const orderTestCases = [
            // === 基础订单类型测试 ===
            {
                name: '接机服务 - 基础',
                type: 'pickup',
                description: '标准机场接机服务',
                data: {
                    sub_category_id: 2,
                    car_type_id: 5,
                    incharge_by_backend_user_id: 1,
                    ota_reference_number: 'PICKUP_' + Date.now(),
                    customer_name: '张三',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA Terminal 1',
                    destination: 'Hotel Sentral',
                    date: '2025-01-15',
                    time: '15:30',
                    passenger_number: 2,
                    luggage_number: 2,
                    extra_requirement: 'TESTING - API测试订单，请勿处理',
                    driving_region_id: 1,
                    languages_id_array: [2, 4]
                }
            },
            {
                name: '送机服务 - 基础',
                type: 'dropoff',
                description: '标准机场送机服务',
                data: {
                    sub_category_id: 3,
                    car_type_id: 15,
                    incharge_by_backend_user_id: 310,
                    ota_reference_number: 'DROPOFF_' + Date.now(),
                    customer_name: '李四',
                    customer_contact: '+60198765432',
                    customer_email: '<EMAIL>',
                    pickup: 'Hotel Sentral',
                    destination: 'KLIA2',
                    date: '2025-01-16',
                    time: '07:00',
                    passenger_number: 4,
                    luggage_number: 4,
                    extra_requirement: 'TESTING - API测试订单，请勿处理',
                    driving_region_id: 1,
                    languages_id_array: [2, 3]
                }
            },
            {
                name: '包车服务 - 基础',
                type: 'charter',
                description: '标准包车服务',
                data: {
                    sub_category_id: 4,
                    car_type_id: 20,
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'CHARTER_' + Date.now(),
                    customer_name: '王五',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    pickup: 'Twin Towers',
                    destination: 'Genting',
                    date: '2025-01-17',
                    time: '08:00',
                    passenger_number: 6,
                    luggage_number: 6,
                    extra_requirement: 'TESTING - API测试订单，请勿处理',
                    driving_region_id: 1,
                    languages_id_array: [2, 4]
                }
            },

            // === 不同用户类型测试 ===
            {
                name: '超级管理员 - 大巴订单',
                type: 'admin',
                description: '超级管理员处理大型团体订单',
                data: {
                    sub_category_id: 4,
                    car_type_id: 26, // 44 Seater Bus
                    incharge_by_backend_user_id: 1, // Super Admin
                    ota_reference_number: 'ADMIN_BUS_' + Date.now(),
                    customer_name: '企业团体',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLCC Convention Centre',
                    destination: 'Genting Highlands Resort',
                    date: '2025-01-22',
                    time: '08:00',
                    passenger_number: 40,
                    luggage_number: 40,
                    driving_region_id: 1,
                    languages_id_array: [2, 3, 4],
                    tour_guide: 1,
                    meet_and_greet: '企业团体',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 企业团建活动，需要专业导游'
                }
            },
            {
                name: '操作员Jcy - VIP接机',
                type: 'operator',
                description: 'Jcy操作员处理VIP接机',
                data: {
                    sub_category_id: 2,
                    car_type_id: 32, // Velfire/Alphard
                    incharge_by_backend_user_id: 310, // Jcy
                    ota_reference_number: 'JCY_VIP_' + Date.now(),
                    customer_name: 'VIP客户',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA First Class Lounge',
                    destination: 'Four Seasons Hotel KL',
                    date: '2025-01-23',
                    time: '16:45',
                    passenger_number: 4,
                    luggage_number: 6,
                    driving_region_id: 1,
                    languages_id_array: [2, 4],
                    tour_guide: 1,
                    meet_and_greet: 'VIP客户',
                    baby_chair: 1,
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要高级导游服务和儿童座椅'
                }
            },
            {
                name: '沙巴分公司 - 当地服务',
                type: 'branch',
                description: '沙巴分公司处理当地旅游',
                data: {
                    sub_category_id: 18, // KK City 5H
                    car_type_id: 20,
                    incharge_by_backend_user_id: 89, // GMH Sabah
                    ota_reference_number: 'SABAH_TOUR_' + Date.now(),
                    customer_name: '沙巴旅行团',
                    customer_contact: '+60128765432',
                    customer_email: '<EMAIL>',
                    pickup: 'Kota Kinabalu Airport',
                    destination: 'Kota Kinabalu City Tour',
                    date: '2025-01-25',
                    time: '09:30',
                    passenger_number: 8,
                    luggage_number: 8,
                    driving_region_id: 4, // Sabah
                    languages_id_array: [2, 4],
                    tour_guide: 1,
                    meet_and_greet: '沙巴旅行团',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 沙巴当地旅游服务'
                }
            },

            // === 不同车型测试 ===
            {
                name: '经济型轿车 - 个人出行',
                type: 'economy',
                description: '经济型4座轿车测试',
                data: {
                    sub_category_id: 2,
                    car_type_id: 38, // 4 Seater Hatchback
                    incharge_by_backend_user_id: 312,
                    ota_reference_number: 'ECONOMY_' + Date.now(),
                    customer_name: '个人乘客',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'KLIA2',
                    destination: 'Kuala Lumpur City',
                    date: '2025-01-26',
                    time: '14:00',
                    passenger_number: 1,
                    luggage_number: 1,
                    driving_region_id: 1,
                    languages_id_array: [2],
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 经济型个人出行'
                }
            },
            {
                name: '豪华轿车 - 商务出行',
                type: 'luxury',
                description: '奔驰/宝马豪华轿车测试',
                data: {
                    sub_category_id: 3,
                    car_type_id: 33, // Premium 5 Seater (Mercedes/BMW)
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'LUXURY_' + Date.now(),
                    customer_name: '商务客户',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    pickup: 'Mandarin Oriental Hotel',
                    destination: 'KLIA Terminal 1',
                    date: '2025-01-27',
                    time: '06:00',
                    passenger_number: 2,
                    luggage_number: 3,
                    driving_region_id: 1,
                    languages_id_array: [2, 4],
                    meet_and_greet: '商务客户',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 豪华商务出行服务'
                }
            },
            {
                name: 'SUV车型 - 家庭出游',
                type: 'suv',
                description: '7座SUV家庭出游测试',
                data: {
                    sub_category_id: 4,
                    car_type_id: 35, // 7 Seater SUV
                    incharge_by_backend_user_id: 310,
                    ota_reference_number: 'SUV_FAMILY_' + Date.now(),
                    customer_name: '家庭客户',
                    customer_contact: '+60176543210',
                    customer_email: '<EMAIL>',
                    pickup: 'Sunway Lagoon Hotel',
                    destination: 'Cameron Highlands',
                    date: '2025-01-28',
                    time: '07:00',
                    passenger_number: 4,
                    luggage_number: 6,
                    driving_region_id: 1,
                    languages_id_array: [2, 4],
                    baby_chair: 1,
                    meet_and_greet: '家庭客户',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要2个儿童座椅'
                }
            },
            {
                name: '小巴车型 - 中型团体',
                type: 'minibus',
                description: '30座小巴中型团体测试',
                data: {
                    sub_category_id: 4,
                    car_type_id: 25, // 30 Seat Mini Bus
                    incharge_by_backend_user_id: 1,
                    ota_reference_number: 'MINIBUS_' + Date.now(),
                    customer_name: '中型旅行团',
                    customer_contact: '+60165432109',
                    customer_email: '<EMAIL>',
                    pickup: 'KL Sentral',
                    destination: 'Malacca Historical City',
                    date: '2025-01-29',
                    time: '08:30',
                    passenger_number: 25,
                    luggage_number: 25,
                    driving_region_id: 12, // Malacca
                    languages_id_array: [2, 3, 4],
                    tour_guide: 1,
                    meet_and_greet: '中型旅行团',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要专业导游和停车费'
                }
            },

            // === 不同驾驶区域测试 ===
            {
                name: '槟城地区 - 当地服务',
                type: 'penang',
                description: '槟城地区当地包车服务',
                data: {
                    sub_category_id: 4,
                    car_type_id: 15,
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'PENANG_' + Date.now(),
                    customer_name: '槟城游客',
                    customer_contact: '+60165432109',
                    customer_email: '<EMAIL>',
                    pickup: 'Penang International Airport',
                    destination: 'Georgetown Heritage Area',
                    date: '2025-01-30',
                    time: '12:00',
                    passenger_number: 5,
                    luggage_number: 5,
                    driving_region_id: 2, // Penang
                    languages_id_array: [2, 3, 4],
                    tour_guide: 1,
                    meet_and_greet: '槟城游客',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 槟城当地旅游服务'
                }
            },
            {
                name: '柔佛地区 - 跨境服务',
                type: 'johor',
                description: '柔佛到新加坡跨境服务',
                data: {
                    sub_category_id: 3,
                    car_type_id: 20,
                    incharge_by_backend_user_id: 312,
                    ota_reference_number: 'JOHOR_SG_' + Date.now(),
                    customer_name: '跨境客户',
                    customer_contact: '+65123456789',
                    customer_email: '<EMAIL>',
                    pickup: 'Johor Bahru CIQ',
                    destination: 'Changi Airport Singapore',
                    date: '2025-01-31',
                    time: '05:00',
                    passenger_number: 3,
                    luggage_number: 4,
                    driving_region_id: 3, // Johor
                    languages_id_array: [2, 4],
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 跨境服务'
                }
            },

            // === 特殊订单类型测试 ===
            {
                name: '天空之镜 - 旅游套餐',
                type: 'tourism',
                description: '天空之镜6小时旅游套餐',
                data: {
                    sub_category_id: 9, // Sky Mirror 6H
                    car_type_id: 15,
                    incharge_by_backend_user_id: 2249, // Skymirror jetty
                    ota_reference_number: 'SKYMIRROR_' + Date.now(),
                    customer_name: '天空之镜游客',
                    customer_contact: '+60176543210',
                    customer_email: '<EMAIL>',
                    pickup: 'Hotel Maya KL',
                    destination: 'Sky Mirror Kuala Selangor',
                    date: '2025-02-01',
                    time: '06:30',
                    passenger_number: 4,
                    luggage_number: 2,
                    driving_region_id: 1,
                    languages_id_array: [2, 4],
                    tour_guide: 1,
                    meet_and_greet: '天空之镜游客',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 包含天空之镜门票和导游服务'
                }
            },
            {
                name: '云顶接驳 - 私人专车',
                type: 'shuttle',
                description: '云顶高原私人接驳服务',
                data: {
                    sub_category_id: 22, // Genting Shuttle Private
                    car_type_id: 32, // Velfire/Alphard
                    incharge_by_backend_user_id: 310,
                    ota_reference_number: 'GENTING_PRIVATE_' + Date.now(),
                    customer_name: '云顶VIP',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    pickup: 'Resorts World Genting',
                    destination: 'KLCC Suria',
                    date: '2025-02-02',
                    time: '20:00',
                    passenger_number: 6,
                    luggage_number: 8,
                    driving_region_id: 1,
                    languages_id_array: [2, 4],
                    meet_and_greet: '云顶VIP',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 云顶高原VIP接驳服务'
                }
            },
            {
                name: '怡保历史游 - 一日游',
                type: 'historical',
                description: '怡保历史城市一日游',
                data: {
                    sub_category_id: 36, // Ipoh City Historical Tour
                    car_type_id: 20,
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'IPOH_TOUR_' + Date.now(),
                    customer_name: '历史爱好者',
                    customer_contact: '+60165432109',
                    customer_email: '<EMAIL>',
                    pickup: 'Ipoh Railway Station',
                    destination: 'Ipoh Old Town Heritage Walk',
                    date: '2025-02-03',
                    time: '09:00',
                    passenger_number: 8,
                    luggage_number: 4,
                    driving_region_id: 1,
                    languages_id_array: [2, 3, 4],
                    tour_guide: 1,
                    meet_and_greet: '历史爱好者',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要历史导游和午餐安排'
                }
            },

            // === 多语言支持测试 ===
            {
                name: '纯英文服务 - 国际客户',
                type: 'english',
                description: '纯英文导游服务测试',
                data: {
                    sub_category_id: 4,
                    car_type_id: 36, // Alphard
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'ENGLISH_' + Date.now(),
                    customer_name: 'John Smith',
                    customer_contact: '+1234567890',
                    customer_email: '<EMAIL>',
                    pickup: 'Mandarin Oriental Hotel',
                    destination: 'Batu Caves Temple',
                    date: '2025-02-04',
                    time: '10:00',
                    passenger_number: 4,
                    luggage_number: 2,
                    driving_region_id: 1,
                    languages_id_array: [2], // English only
                    tour_guide: 1,
                    meet_and_greet: 'John Smith',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - English-speaking guide required'
                }
            },
            {
                name: '马来语服务 - 本地客户',
                type: 'malay',
                description: '马来语导游服务测试',
                data: {
                    sub_category_id: 4,
                    car_type_id: 15,
                    incharge_by_backend_user_id: 312,
                    ota_reference_number: 'MALAY_' + Date.now(),
                    customer_name: 'Ahmad Abdullah',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'Masjid Negara',
                    destination: 'Putrajaya Islamic Center',
                    date: '2025-02-05',
                    time: '14:00',
                    passenger_number: 5,
                    luggage_number: 3,
                    driving_region_id: 1,
                    languages_id_array: [3], // Malay only
                    tour_guide: 1,
                    meet_and_greet: 'Ahmad Abdullah',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 马来语导游服务'
                }
            },
            {
                name: '三语服务 - 混合团体',
                type: 'multilingual',
                description: '英语+马来语+中文三语服务',
                data: {
                    sub_category_id: 4,
                    car_type_id: 25, // 30 Seat Mini Bus
                    incharge_by_backend_user_id: 1,
                    ota_reference_number: 'TRILINGUAL_' + Date.now(),
                    customer_name: '国际混合团',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    pickup: 'KLCC Convention Centre',
                    destination: 'Malacca UNESCO Sites',
                    date: '2025-02-06',
                    time: '08:00',
                    passenger_number: 20,
                    luggage_number: 20,
                    driving_region_id: 12, // Malacca
                    languages_id_array: [2, 3, 4], // English, Malay, Chinese
                    tour_guide: 1,
                    meet_and_greet: '国际混合团',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要三语导游，能够流利切换语言'
                }
            },

            // === 边界和压力测试 ===
            {
                name: '最小字段 - 边界测试',
                type: 'minimal',
                description: '完整最小字段测试，确保数据一致性',
                data: {
                    sub_category_id: 2,
                    car_type_id: 5,
                    incharge_by_backend_user_id: 1,
                    ota_reference_number: 'MINIMAL_' + Date.now(),
                    customer_name: 'API测试用户',
                    customer_contact: '+***********',
                    customer_email: '<EMAIL>',
                    pickup: 'Pavilion Kuala Lumpur',
                    destination: 'Sunway Pyramid Shopping Mall',
                    date: '2025-01-15',
                    time: '14:00',
                    passenger_number: 2,
                    luggage_number: 2,
                    driving_region_id: 1, // 明确指定KL/Selangor
                    languages_id_array: [2, 4], // 明确指定English + Chinese
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 最小字段边界测试（完整版，确保后台数据一致性）'
                }
            },
            {
                name: '大型团体 - 边界测试',
                type: 'stress',
                description: '测试大容量车型边界',
                data: {
                    sub_category_id: 4,
                    car_type_id: 26, // 44 Seater Bus
                    incharge_by_backend_user_id: 1,
                    ota_reference_number: 'LARGE_' + Date.now(),
                    customer_name: '大型团体',
                    customer_contact: '+60165432109',
                    customer_email: '<EMAIL>',
                    passenger_number: 35, // 修正为35人，符合43座巴士容量
                    luggage_number: 35,
                    pickup: 'KLIA',
                    destination: 'Genting',
                    date: '2025-02-07',
                    time: '10:00',
                    driving_region_id: 1,
                    languages_id_array: [2, 3, 4],
                    tour_guide: 1,
                    meet_and_greet: '大型团体',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 大型团体旅游，需要专业导游'
                }
            },
            {
                name: '最大字段 - 完整测试',
                type: 'maximal',
                description: '包含所有可选字段',
                data: {
                    sub_category_id: 4,
                    car_type_id: 36, // Alphard
                    incharge_by_backend_user_id: 311,
                    ota_reference_number: 'MAXIMAL_' + Date.now(),
                    ota: 'Premium OTA',
                    ota_price: 500.00,
                    customer_name: '完整测试客户',
                    customer_contact: '+60187654321',
                    customer_email: '<EMAIL>',
                    flight_info: 'MH123 - 14:30 Arrival',
                    pickup: 'KLIA Terminal 1 Arrival Hall',
                    pickup_lat: 2.745578,
                    pickup_long: 101.709917,
                    date: '2025-02-08',
                    time: '15:00',
                    destination: 'Four Seasons Hotel Kuala Lumpur',
                    destination_lat: 3.147456,
                    destination_long: 101.711050,
                    passenger_number: 6,
                    luggage_number: 8,
                    driver_fee: 350.00,
                    driver_collect: 50.00,
                    tour_guide: 1,
                    baby_chair: 1,
                    meet_and_greet: '完整测试客户',
                    extra_requirement: 'TESTING - API测试订单，请勿处理 - 需要全套服务：导游、儿童座椅、接机牌、特殊路线',
                    driving_region_id: 1,
                    languages_id_array: [2, 3, 4]
                }
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('GoMyHire API多账号认证测试工具初始化');
            try {
                renderOrderTestCards();
                updateOrderTestStats();
                initializeAddressTemplates(); // 初始化地址模板
                initializeAccountSelector(); // 初始化账号选择器

                // 自动切换到默认邮箱账号
                updateAuthStatusIndicator(false, '正在初始化多账号认证...');
                updateCurrentAccountInfo('正在加载默认邮箱账号...');

                const defaultAccount = realLoginAccounts.find(acc => acc.isDefault);
                if (defaultAccount) {
                    await switchToAccount(defaultAccount.id);
                } else {
                    console.warn('未找到默认邮箱账号，请手动选择账号');
                    updateAuthStatusIndicator(false, '请选择邮箱账号');
                    updateCurrentAccountInfo('请从上方选择邮箱账号');
                }

                console.log('多账号认证测试工具初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                updateAuthStatusIndicator(false, '❌ 初始化失败');
                updateCurrentAccountInfo('初始化失败');
                showError('页面初始化失败: ' + error.message);
            }
        });

        // === 无认证模式说明函数 ===
        
        // 显示无认证模式状态
        function updateOrderTestStatus(message, type) {
            console.log(`订单测试状态: ${message} (${type})`);
            
            // 可以在这里添加状态显示逻辑
            const statusElement = document.createElement('div');
            statusElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                border-radius: 4px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            
            if (type === 'success') {
                statusElement.style.background = '#28a745';
            } else if (type === 'error') {
                statusElement.style.background = '#dc3545';
            } else {
                statusElement.style.background = '#007bff';
            }
            
            statusElement.textContent = message;
            document.body.appendChild(statusElement);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 3000);
        }

        // 更新认证状态指示器（无需认证模式）
        function updateAuthStatusIndicator(isNoAuthMode) {
            const indicator = safeGetElement('authStatusIndicator');
            const statusText = safeGetElement('authStatusText');

            if (!indicator || !statusText) return;

            if (isNoAuthMode) {
                indicator.style.background = '#d4edda';
                indicator.style.borderColor = '#c3e6cb';
                statusText.style.color = '#155724';
                statusText.textContent = '✅ 无需认证模式已启用，可以直接开始测试';
            } else {
                indicator.style.background = '#fff3cd';
                indicator.style.borderColor = '#ffeaa7';
                statusText.style.color = '#856404';
                statusText.textContent = '⚠️ 无认证模式，某些API可能返回限制错误';
            }
        }

        // 启用数据加载按钮
        function enableDataLoadButtons() {
            const buttons = ['loadDataBtn', 'loadUsersBtn', 'loadCategoriesBtn', 'loadCarsBtn'];
            buttons.forEach(id => {
                const button = safeGetElement(id);
                if (button) {
                    button.disabled = false;
                }
            });
        }

        // 加载所有系统数据（认证模式）
        async function loadAllSystemData() {
            console.log('开始加载系统数据（认证模式）...');

            if (!authToken) {
                console.warn('没有认证token，使用本地预设数据');
                loadLocalSystemData();
                return;
            }

            try {
                updateOrderTestStatus('正在从Live API加载系统数据...', 'info');

                // 准备认证头部
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                };

                // 并行加载所有系统数据
                const [backendUsersRes, subCategoriesRes, carTypesRes, drivingRegionsRes, languagesRes] = await Promise.all([
                    fetch(`${API_BASE_URL}/backend_users?search=`, { headers }),
                    fetch(`${API_BASE_URL}/sub_categories?search=`, { headers }),
                    fetch(`${API_BASE_URL}/car_types?search=`, { headers }),
                    fetch(`${API_BASE_URL}/driving_regions?search=`, { headers }),
                    fetch(`${API_BASE_URL}/languages?search=`, { headers })
                ]);

                // 解析响应
                systemData.backendUsers = backendUsersRes.ok ? (await backendUsersRes.json()).data || [] : [];
                systemData.subCategories = subCategoriesRes.ok ? (await subCategoriesRes.json()).data || [] : [];
                systemData.carTypes = carTypesRes.ok ? (await carTypesRes.json()).data || [] : [];
                systemData.drivingRegions = drivingRegionsRes.ok ? (await drivingRegionsRes.json()).data || [] : [];
                systemData.languages = languagesRes.ok ? (await languagesRes.json()).data || [] : [];

                displaySystemData();
                console.log('系统数据加载完成（从Live API获取）');
                console.log('系统数据统计:', {
                    backendUsers: systemData.backendUsers.length,
                    subCategories: systemData.subCategories.length,
                    carTypes: systemData.carTypes.length,
                    drivingRegions: systemData.drivingRegions.length,
                    languages: systemData.languages.length
                });
                updateOrderTestStatus('系统数据加载完成（Live API）', 'success');

            } catch (error) {
                console.warn('从API加载系统数据失败，使用本地预设数据:', error.message);
                loadLocalSystemData();
            }
        }

        // 加载本地预设系统数据
        function loadLocalSystemData() {
            systemData = {
                backendUsers: [
                    { id: 1, name: "Super Admin", phone: "0123456789", role: "admin" },
                    { id: 310, name: "Jcy", phone: "0123456790", role: "operator" },
                    { id: 311, name: "Operator A", phone: "0123456791", role: "operator" },
                    { id: 312, name: "Operator B", phone: "0123456792", role: "operator" },
                    { id: 89, name: "GMH Sabah", phone: "0123456793", role: "branch" }
                ],
                subCategories: [
                    { id: 2, main_category: "Airport Transfer", name: "举牌接机", preset_data: {}, required_fields: [] },
                    { id: 3, main_category: "Airport Transfer", name: "送机", preset_data: {}, required_fields: [] },
                    { id: 4, main_category: "Hourly Charter", name: "包车服务", preset_data: {}, required_fields: [] },
                    { id: 18, main_category: "KK City Tour", name: "KK City 5H", preset_data: {}, required_fields: [] }
                ],
                carTypes: [
                    { id: 5, type: "Comfort 5 Seater", seat_number: 5, priority: 1 },
                    { id: 15, type: "Premium 5 Seater", seat_number: 5, priority: 2 },
                    { id: 20, type: "Luxury 7 Seater", seat_number: 7, priority: 3 },
                    { id: 26, type: "44 Seater Bus", seat_number: 44, priority: 4 },
                    { id: 32, type: "Velfire/Alphard", seat_number: 7, priority: 5 },
                    { id: 38, type: "4 Seater Hatchback", seat_number: 4, priority: 6 }
                ],
                drivingRegions: [
                    { id: 1, name: "Kuala Lumpur" },
                    { id: 4, name: "Sabah" }
                ],
                languages: [
                    { id: 2, name: "English" },
                    { id: 3, name: "Mandarin" },
                    { id: 4, name: "Malay" }
                ]
            };

            displaySystemData();
            console.log('本地预设系统数据加载完成');
            updateOrderTestStatus('系统数据加载完成（本地预设）', 'success');
        }

        // 加载后台用户（无认证模式）
        async function loadBackendUsers() {
            try {
                // 使用本地预设数据
                systemData.backendUsers = [
                    { id: 1, name: "Super Admin", phone: "0123456789", role: "admin" },
                    { id: 310, name: "Jcy", phone: "0123456790", role: "operator" },
                    { id: 311, name: "Operator A", phone: "0123456791", role: "operator" },
                    { id: 312, name: "Operator B", phone: "0123456792", role: "operator" },
                    { id: 89, name: "GMH Sabah", phone: "0123456793", role: "branch" }
                ];
                safeSetTextContent('usersCount', systemData.backendUsers.length);
                console.log('后台用户数据加载完成（无认证模式）', systemData.backendUsers.length);

            } catch (error) {
                console.error('后台用户加载失败:', error);
                throw error;
            }
        }

        // 加载子分类（无认证模式）
        async function loadSubCategories() {
            try {
                // 使用本地预设数据
                systemData.subCategories = [
                    { id: 2, main_category: "Airport Transfer", name: "举牌接机", preset_data: {}, required_fields: [] },
                    { id: 3, main_category: "Airport Transfer", name: "送机", preset_data: {}, required_fields: [] },
                    { id: 4, main_category: "Hourly Charter", name: "包车服务", preset_data: {}, required_fields: [] },
                    { id: 18, main_category: "KK City Tour", name: "KK City 5H", preset_data: {}, required_fields: [] }
                ];
                safeSetTextContent('categoriesCount', systemData.subCategories.length);
                console.log('子分类数据加载完成（无认证模式）', systemData.subCategories.length);

            } catch (error) {
                console.error('子分类加载失败:', error);
                throw error;
            }
        }

        // 加载车型（无认证模式）
        async function loadCarTypes() {
            try {
                // 使用本地预设数据
                systemData.carTypes = [
                    { id: 5, type: "Comfort 5 Seater", seat_number: 5, priority: 1 },
                    { id: 15, type: "Premium 5 Seater", seat_number: 5, priority: 2 },
                    { id: 20, type: "Luxury 7 Seater", seat_number: 7, priority: 3 },
                    { id: 26, type: "44 Seater Bus", seat_number: 44, priority: 4 },
                    { id: 32, type: "Velfire/Alphard", seat_number: 7, priority: 5 },
                    { id: 38, type: "4 Seater Hatchback", seat_number: 4, priority: 6 }
                ];
                safeSetTextContent('carTypesCount', systemData.carTypes.length);
                console.log('车型数据加载完成（无认证模式）', systemData.carTypes.length);

            } catch (error) {
                console.error('车型加载失败:', error);
                throw error;
            }
        }

        // 加载行驶区域（无认证模式）
        async function loadDrivingRegions() {
            try {
                // 使用本地预设数据
                systemData.drivingRegions = [
                    { id: 1, name: "Kuala Lumpur" },
                    { id: 4, name: "Sabah" }
                ];
                safeSetTextContent('regionsCount', systemData.drivingRegions.length);
                console.log('行驶区域数据加载完成（无认证模式）', systemData.drivingRegions.length);
            } catch (error) {
                console.warn('行驶区域加载失败:', error);
            }
        }

        // 加载语言（无认证模式）
        async function loadLanguages() {
            try {
                // 使用本地预设数据
                systemData.languages = [
                    { id: 2, name: "English" },
                    { id: 3, name: "Mandarin" },
                    { id: 4, name: "Malay" }
                ];
                safeSetTextContent('languagesCount', systemData.languages.length);
                console.log('语言数据加载完成（无认证模式）', systemData.languages.length);
            } catch (error) {
                console.warn('语言加载失败:', error);
            }
        }

        // 显示系统数据
        function displaySystemData() {
            const display = safeGetElement('systemDataDisplay');
            const content = safeGetElement('systemDataContent');
            
            if (!display || !content) return;
            
            let html = '';
            
            if (systemData.backendUsers.length > 0) {
                html += '<h5>后台用户 (前5个)</h5>';
                systemData.backendUsers.slice(0, 5).forEach(user => {
                    html += `<div class="data-item">ID: ${user.id}, 姓名: ${user.name}, 角色: ${user.role}</div>`;
                });
            }

            if (systemData.subCategories.length > 0) {
                html += '<h5>服务类型 (前5个)</h5>';
                systemData.subCategories.slice(0, 5).forEach(cat => {
                    html += `<div class="data-item">ID: ${cat.id}, 名称: ${cat.name}</div>`;
                });
            }

            if (systemData.carTypes.length > 0) {
                html += '<h5>车型 (前5个)</h5>';
                systemData.carTypes.slice(0, 5).forEach(car => {
                    html += `<div class="data-item">ID: ${car.id}, 名称: ${car.type}</div>`;
                });
            }
            
            content.innerHTML = html;
            display.style.display = 'block';
        }

        // 渲染订单测试用例（别名函数支持地址模板）
        function renderOrderTestCases() {
            renderOrderTestCards();
        }

        // 渲染订单测试卡片
        function renderOrderTestCards() {
            const container = safeGetElement('orderTestGrid');
            if (!container) return;
            
            container.innerHTML = '';
            
            orderTestCases.forEach((testCase, index) => {
                const card = document.createElement('div');
                card.className = 'test-card';
                
                const typeColor = testCase.type === 'pickup' ? '#28a745' : 
                                testCase.type === 'dropoff' ? '#007bff' : 
                                testCase.type === 'charter' ? '#ffc107' :
                                testCase.type === 'advanced' ? '#17a2b8' : '#dc3545';
                
                // 提取地址信息用于显示
                const pickup = testCase.data.pickup || 'N/A';
                const destination = testCase.data.destination || 'N/A';
                
                card.innerHTML = `
                    <div class="test-card-header">
                        <h4 class="test-title">${testCase.name}</h4>
                        <span class="test-type" style="background: ${typeColor};">${testCase.type}</span>
                    </div>
                    <div class="test-card-body">
                        <p style="margin: 5px 0; color: #6c757d; font-size: 0.9em;">${testCase.description}</p>
                        
                        ${pickup !== 'N/A' || destination !== 'N/A' ? `
                        <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 8px 0; font-size: 0.85em;">
                            <div style="color: #28a745; font-weight: 500;">📍 接机: ${pickup}</div>
                            <div style="color: #dc3545; font-weight: 500;">🎯 送达: ${destination}</div>
                        </div>
                        ` : ''}
                        
                        <details style="margin: 8px 0;">
                            <summary style="cursor: pointer; color: #6c757d; font-size: 0.9em;">查看完整数据</summary>
                            <div class="test-data" style="margin-top: 8px;">${JSON.stringify(testCase.data, null, 2)}</div>
                        </details>
                        
                        <button class="btn btn-primary" onclick="safeExecute(runSingleOrderTest, ${index})">
                            测试此订单
                        </button>
                        <div id="orderResult${index}" class="test-result" style="display: none;"></div>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        /**
         * @function prepareOrderData - 准备订单数据，同步当前认证信息
         * @param {Object} originalData - 原始测试数据
         * @returns {Object} 处理后的订单数据
         */
        function prepareOrderData(originalData) {
            // 深拷贝原始数据
            const orderData = JSON.parse(JSON.stringify(originalData));

            // 同步当前选中的后台用户ID
            if (selectedBackendUser && selectedBackendUser.id) {
                orderData.incharge_by_backend_user_id = selectedBackendUser.id;
                console.log(`使用当前选中的后台用户: ${selectedBackendUser.name} (ID: ${selectedBackendUser.id})`);
            } else if (availableBackendUsers && availableBackendUsers.length > 0) {
                // 如果没有选中用户，使用第一个可用用户
                orderData.incharge_by_backend_user_id = availableBackendUsers[0].id;
                console.log(`使用第一个可用后台用户: ${availableBackendUsers[0].name} (ID: ${availableBackendUsers[0].id})`);
            } else {
                console.log(`使用原始后台用户ID: ${orderData.incharge_by_backend_user_id}`);
            }

            return orderData;
        }

        /**
         * @function validateAuthenticationStatus - 验证认证状态
         * @returns {Object} 认证状态信息
         */
        function validateAuthenticationStatus() {
            const status = {
                hasToken: !!authToken,
                hasAccount: !!currentAccount,
                hasBackendUser: !!selectedBackendUser,
                message: ''
            };

            if (!status.hasToken) {
                status.message = '未找到认证token，请先登录邮箱账号';
            } else if (!status.hasAccount) {
                status.message = '未找到当前账号信息';
            } else if (!status.hasBackendUser && availableBackendUsers.length === 0) {
                status.message = '未找到可用的后台用户';
            } else {
                status.message = '认证状态正常';
            }

            return status;
        }

        // 运行单个订单测试
        async function runSingleOrderTest(index) {
            if (index < 0 || index >= orderTestCases.length) {
                console.error('无效的测试用例索引:', index);
                return;
            }

            const testCase = orderTestCases[index];
            const resultContainer = safeGetElement(`orderResult${index}`);

            if (!resultContainer) {
                console.error('找不到结果容器:', `orderResult${index}`);
                return;
            }

            resultContainer.style.display = 'block';
            resultContainer.className = 'test-result result-pending';
            resultContainer.innerHTML = '🔄 测试进行中...';

            try {
                // 验证认证状态
                const authStatus = validateAuthenticationStatus();
                console.log(`认证状态检查: ${authStatus.message}`);

                if (!authStatus.hasToken) {
                    throw new Error(`认证失败: ${authStatus.message}`);
                }

                // 准备订单数据（同步当前认证信息）
                const orderData = prepareOrderData(testCase.data);
                console.log(`开始测试订单: ${testCase.name}`, {
                    originalData: testCase.data,
                    processedData: orderData,
                    currentAccount: currentAccount?.email,
                    selectedBackendUser: selectedBackendUser?.name
                });

                const startTime = Date.now();

                // 准备请求头部
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                };

                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(orderData)
                });

                const responseTime = Date.now() - startTime;

                // 获取响应文本
                const responseText = await response.text();
                console.log(`API响应详情:`, {
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url,
                    headers: Object.fromEntries(response.headers.entries()),
                    responseText: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''),
                    requestData: testCase.data
                });

                // 检查响应状态
                if (!response.ok) {
                    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                    let debugInfo = '';

                    // 特殊处理500错误
                    if (response.status === 500) {
                        errorMessage = `服务器内部错误 (HTTP 500)`;
                        debugInfo = `可能原因: 1)API需要认证 2)数据格式错误 3)服务器配置问题`;

                        // 尝试解析错误响应
                        try {
                            const errorData = JSON.parse(responseText);
                            if (errorData.message) {
                                debugInfo += ` | 服务器消息: ${errorData.message}`;
                            }
                            if (errorData.error) {
                                debugInfo += ` | 错误详情: ${errorData.error}`;
                            }
                        } catch (e) {
                            // 如果不是JSON，检查是否是HTML错误页面
                            if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
                                const titleMatch = responseText.match(/<title>(.*?)<\/title>/i);
                                if (titleMatch) {
                                    debugInfo += ` | HTML错误页面: ${titleMatch[1]}`;
                                }
                            } else {
                                debugInfo += ` | 原始响应: ${responseText.substring(0, 100)}`;
                            }
                        }
                    }

                    // 如果响应是HTML，提取错误信息
                    if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
                        const titleMatch = responseText.match(/<title>(.*?)<\/title>/i);
                        if (titleMatch) {
                            errorMessage += ` - ${titleMatch[1]}`;
                        }

                        // 检查是否是404错误
                        if (response.status === 404) {
                            errorMessage += ' - API端点不存在，请检查URL是否正确';
                        }

                        console.error('服务器返回HTML页面:', responseText.substring(0, 500));
                    }

                    // 添加调试信息到错误消息
                    if (debugInfo) {
                        errorMessage += `\n调试信息: ${debugInfo}`;
                    }

                    throw new Error(errorMessage);
                }
                
                // 尝试解析JSON响应
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析失败:', { responseText, parseError });
                    
                    // 如果响应是HTML页面
                    if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
                        throw new Error('服务器返回HTML页面而不是JSON响应，可能是API端点错误或服务器内部错误');
                    } else {
                        throw new Error(`无效的JSON响应: ${parseError.message}`);
                    }
                }
                
                // 检查API响应状态
                if (result.status === true || result.status === 'true') {
                    orderTestStats.success++;
                    orderTestStats.total++;
                    
                    resultContainer.className = 'test-result result-success';
                    resultContainer.innerHTML = `
                        ✅ <strong>测试成功</strong><br>
                        订单ID: ${result.order_id || result.data?.order_id || result.data?.id || 'N/A'}<br>
                        消息: ${result.message || '订单创建成功'}<br>
                        <small>响应时间: ${responseTime}ms</small>
                    `;
                    
                    console.log(`订单测试成功: ${testCase.name}`, result);
                    
                } else {
                    // API返回失败状态
                    const errorMessage = result.message || result.error || '订单创建失败';
                    const validationErrors = result.data?.validation_error;
                    
                    let errorDetails = errorMessage;
                    if (validationErrors) {
                        const errorList = Object.entries(validationErrors)
                            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
                            .join('; ');
                        errorDetails += ` (验证错误: ${errorList})`;
                    }
                    
                    throw new Error(errorDetails);
                }
                
            } catch (error) {
                orderTestStats.failed++;
                orderTestStats.total++;
                
                resultContainer.className = 'test-result result-error';
                resultContainer.innerHTML = `
                    ❌ <strong>测试失败</strong><br>
                    错误: ${error.message}<br>
                    <small>测试用例: ${testCase.name}</small>
                `;
                
                console.error(`订单测试失败: ${testCase.name}`, error);
                
                // 如果是网络错误或解析错误，提供更多调试信息
                if (error.name === 'TypeError' || error.message.includes('fetch')) {
                    console.error('网络请求失败，请检查:', {
                        url: `${API_BASE_URL}/create_order`,
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: testCase.data
                    });
                }
            }
            
            updateOrderTestStats();
        }

        // 运行所有订单测试
        async function runAllOrderTests() {
            updateOrderTestStatus('开始运行所有测试...', 'pending');
            
            for (let i = 0; i < orderTestCases.length; i++) {
                await runSingleOrderTest(i);
                // 添加延迟避免API限制
                if (i < orderTestCases.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            
            updateOrderTestStatus('所有测试完成', 'success');
        }

        // 运行基础订单测试
        async function runBasicOrderTests() {
            updateOrderTestStatus('开始运行基础测试...', 'pending');

            const basicTestIndices = [0, 1, 2, 5]; // 接机、送机、包车、最小字段

            for (const index of basicTestIndices) {
                await runSingleOrderTest(index);
                // 添加延迟避免API限制
                if (index !== basicTestIndices[basicTestIndices.length - 1]) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            
            updateOrderTestStatus('基础测试完成', 'success');
        }

        // 运行高级订单测试
        async function runAdvancedOrderTests() {
            updateOrderTestStatus('开始运行高级测试...', 'pending');

            const advancedTestIndices = [3, 4]; // 天空之镜、大型团体

            for (const index of advancedTestIndices) {
                await runSingleOrderTest(index);
                // 添加延迟避免API限制
                if (index !== advancedTestIndices[advancedTestIndices.length - 1]) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            
            updateOrderTestStatus('高级测试完成', 'success');
        }

        // 更新订单测试统计
        function updateOrderTestStats() {
            safeSetTextContent('totalTests', orderTestStats.total);
            safeSetTextContent('successTests', orderTestStats.success);
            safeSetTextContent('failedTests', orderTestStats.failed);
            
            const successRate = orderTestStats.total > 0 ? 
                Math.round((orderTestStats.success / orderTestStats.total) * 100) : 0;
            safeSetTextContent('successRate', `${successRate}%`);
        }

        // 更新订单测试状态（支持地址模板）
        function updateOrderTestStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.style.cssText = `
                padding: 8px 12px;
                margin: 10px 0;
                border-radius: 4px;
                font-size: 0.9em;
                background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#e2e3e5'};
                color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#383d41'};
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#d1d3d4'};
            `;
            statusDiv.textContent = message;
            
            // 插入到地址模板区域下方
            const addressTemplates = document.querySelector('.address-templates');
            if (addressTemplates && addressTemplates.parentNode) {
                addressTemplates.parentNode.insertBefore(statusDiv, addressTemplates.nextSibling);
                
                // 3秒后自动移除
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 3000);
            }
        }

        // 清除所有测试结果
        function clearAllResults() {
            orderTestStats = { total: 0, success: 0, failed: 0 };
            updateOrderTestStats();

            // 清除所有测试结果显示
            orderTestCases.forEach((_, index) => {
                const resultContainer = safeGetElement(`orderResult${index}`);
                if (resultContainer) {
                    resultContainer.style.display = 'none';
                    resultContainer.className = 'test-result';
                    resultContainer.innerHTML = '';
                }
            });

            console.log('所有测试结果已清除');
        }

        // === API诊断工具函数 ===

        // 显示诊断结果
        function showDiagnosticResult(message) {
            const resultDiv = safeGetElement('diagnosticResults');
            if (resultDiv) {
                resultDiv.style.display = 'block';
                resultDiv.textContent += message + '\n';
            }
            console.log('诊断结果:', message);
        }

        // 清除诊断结果
        function clearDiagnosticResults() {
            const resultDiv = safeGetElement('diagnosticResults');
            if (resultDiv) {
                resultDiv.textContent = '';
                resultDiv.style.display = 'none';
            }
        }

        // API健康检查
        async function runAPIHealthCheck() {
            clearDiagnosticResults();
            showDiagnosticResult('=== API健康检查开始 ===');

            try {
                // 1. 检查API基础连接
                showDiagnosticResult('1. 检查API基础连接...');
                const startTime = Date.now();

                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'OPTIONS'
                });

                const responseTime = Date.now() - startTime;
                showDiagnosticResult(`   响应时间: ${responseTime}ms`);
                showDiagnosticResult(`   状态码: ${response.status}`);
                showDiagnosticResult(`   状态文本: ${response.statusText}`);

                // 2. 检查CORS头部
                showDiagnosticResult('2. 检查CORS配置...');
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                showDiagnosticResult(`   CORS头部: ${JSON.stringify(corsHeaders, null, 2)}`);

                // 3. 检查服务器类型
                showDiagnosticResult('3. 检查服务器信息...');
                showDiagnosticResult(`   服务器: ${response.headers.get('Server') || '未知'}`);
                showDiagnosticResult(`   内容类型: ${response.headers.get('Content-Type') || '未知'}`);

            } catch (error) {
                showDiagnosticResult(`❌ 连接失败: ${error.message}`);
                if (error.name === 'TypeError') {
                    showDiagnosticResult('   可能原因: 网络连接问题或CORS阻止');
                }
            }

            showDiagnosticResult('=== API健康检查完成 ===');
        }

        // 测试最小请求
        async function testMinimalRequest() {
            clearDiagnosticResults();
            showDiagnosticResult('=== 最小请求测试开始 ===');

            const minimalData = {
                sub_category_id: 2,
                car_type_id: 5,
                incharge_by_backend_user_id: 1,
                ota_reference_number: 'TEST_MINIMAL_' + Date.now(),
                customer_name: 'Test User',
                customer_contact: '+***********',
                customer_email: '<EMAIL>',
                pickup: 'Test Pickup',
                destination: 'Test Destination',
                date: '2025-01-15',
                time: '15:30',
                passenger_number: 1,
                luggage_number: 1,
                driving_region_id: 1,
                languages_id_array: [2]
            };

            try {
                showDiagnosticResult('发送最小数据请求...');
                showDiagnosticResult(`请求数据: ${JSON.stringify(minimalData, null, 2)}`);

                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(minimalData)
                });

                showDiagnosticResult(`响应状态: ${response.status} ${response.statusText}`);

                const responseText = await response.text();
                showDiagnosticResult(`响应内容: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`);

                if (response.status === 500) {
                    showDiagnosticResult('❌ 服务器内部错误 - 可能需要认证或数据格式问题');
                } else if (response.status === 401) {
                    showDiagnosticResult('❌ 认证失败 - API需要认证token');
                } else if (response.status === 422) {
                    showDiagnosticResult('❌ 数据验证失败 - 检查字段格式');
                } else if (response.ok) {
                    showDiagnosticResult('✅ 请求成功');
                }

            } catch (error) {
                showDiagnosticResult(`❌ 请求失败: ${error.message}`);
            }

            showDiagnosticResult('=== 最小请求测试完成 ===');
        }

        // 测试带认证的请求
        async function testWithAuth() {
            clearDiagnosticResults();
            showDiagnosticResult('=== 认证请求测试开始 ===');
            showDiagnosticResult('使用Live环境API进行认证测试');

            const testData = {
                sub_category_id: 2,
                car_type_id: 5,
                incharge_by_backend_user_id: 1,
                ota_reference_number: 'TEST_AUTH_' + Date.now(),
                customer_name: 'Test User',
                customer_contact: '+***********',
                customer_email: '<EMAIL>',
                pickup: 'Test Pickup',
                destination: 'Test Destination',
                date: '2025-01-15',
                time: '15:30',
                passenger_number: 1,
                luggage_number: 1,
                driving_region_id: 1,
                languages_id_array: [2]
            };

            try {
                showDiagnosticResult('\n1. 获取认证token...');
                const token = await getAuthToken();

                if (token) {
                    showDiagnosticResult(`   ✅ Token获取成功: ${token.substring(0, 20)}...`);

                    // 使用token测试create_order
                    showDiagnosticResult('\n2. 使用token测试create_order...');
                    const orderResponse = await fetch(`${API_BASE_URL}/create_order`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(testData)
                    });

                    showDiagnosticResult(`   订单创建状态: ${orderResponse.status} ${orderResponse.statusText}`);

                    const orderResponseText = await orderResponse.text();
                    showDiagnosticResult(`   订单创建响应: ${orderResponseText.substring(0, 500)}${orderResponseText.length > 500 ? '...' : ''}`);

                    if (orderResponse.ok) {
                        showDiagnosticResult('✅ 带认证的请求成功！');
                    } else {
                        showDiagnosticResult('❌ 带认证的请求失败');
                    }
                } else {
                    showDiagnosticResult('❌ 无法获取认证token');
                }

            } catch (error) {
                showDiagnosticResult(`❌ 认证测试失败: ${error.message}`);
            }

            showDiagnosticResult('\n=== 认证请求测试完成 ===');
        }

        // 显示账号测试结果
        function displayAccountTestResults(results) {
            const section = safeGetElement('accountTestResultsSection');
            const container = safeGetElement('accountTestResults');

            if (!section || !container) return;

            // 显示结果区域
            section.style.display = 'block';

            // 生成结果表格
            let html = `
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">邮箱账号</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">认证</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">后台用户数</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">车型</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">分类</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">区域</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">语言</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">创建订单</th>
                                <th style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">权限评分</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            results.forEach(result => {
                const account = result.account;
                const authIcon = result.authSuccess ? '✅' : '❌';

                // 计算权限评分
                const totalPermissions = Object.keys(result.permissions).length;
                const successfulPermissions = Object.values(result.permissions).filter(p => p.success).length;
                const permissionScore = totalPermissions > 0 ? Math.round((successfulPermissions / totalPermissions) * 100) : 0;

                // 生成权限状态图标
                const getPermissionIcon = (permName) => {
                    const perm = result.permissions[permName];
                    if (!perm) return '❓';
                    if (perm.success) {
                        return perm.hasData ? '✅' : '🔍';
                    }
                    return perm.status === 403 ? '🚫' : '❌';
                };

                const createOrderIcon = result.createOrderTest ?
                    (result.createOrderTest.success ? '✅' : '❌') : '❓';

                // 后台用户数量显示
                const backendUserCount = result.backendUsers ? result.backendUsers.length : 0;
                const backendUserDisplay = backendUserCount > 0 ?
                    `${backendUserCount}个` :
                    (result.authSuccess ? '0个' : '未获取');

                html += `
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">
                            <div style="font-weight: bold;">${account.email}</div>
                            <div style="font-size: 0.8em; color: #666;">
                                ${result.selectedBackendUser ? `当前: ${result.selectedBackendUser.name}` : '未选择后台用户'}
                            </div>
                        </td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${authIcon}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${backendUserDisplay}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${getPermissionIcon('car_types')}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${getPermissionIcon('sub_categories')}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${getPermissionIcon('driving_regions')}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${getPermissionIcon('languages')}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">${createOrderIcon}</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px; text-align: center;">
                            <span style="color: ${permissionScore >= 80 ? '#28a745' : permissionScore >= 50 ? '#ffc107' : '#dc3545'};">
                                ${permissionScore}%
                            </span>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <h6>图标说明：</h6>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 0.9em;">
                        <div>✅ 成功访问且有数据</div>
                        <div>🔍 成功访问但无数据</div>
                        <div>🚫 权限不足 (403)</div>
                        <div>❌ 访问失败</div>
                        <div>❓ 未测试</div>
                    </div>
                </div>
            `;

            container.innerHTML = html;

            // 生成详细报告
            generateDetailedReport(results);
        }

        // 生成详细测试报告
        function generateDetailedReport(results) {
            console.log('\n=== 多账号测试详细报告 ===');

            results.forEach(result => {
                const account = result.account;
                console.log(`\n邮箱账号: ${account.email}`);
                console.log(`认证状态: ${result.authSuccess ? '成功' : '失败'}`);

                if (result.authSuccess) {
                    // 后台用户信息
                    const backendUserCount = result.backendUsers ? result.backendUsers.length : 0;
                    console.log(`后台用户: 共${backendUserCount}个可用`);
                    if (result.selectedBackendUser) {
                        console.log(`  选中用户: ${result.selectedBackendUser.name} (ID: ${result.selectedBackendUser.id})`);
                    }

                    console.log('API权限测试结果:');
                    Object.entries(result.permissions).forEach(([api, perm]) => {
                        const status = perm.success ? '✅' : '❌';
                        const dataInfo = perm.hasData ? ` (${perm.dataCount}条数据)` : ' (无数据)';
                        console.log(`  ${api}: ${status} HTTP ${perm.status}${perm.success ? dataInfo : ''}`);
                    });

                    if (result.createOrderTest) {
                        const orderStatus = result.createOrderTest.success ? '✅' : '❌';
                        console.log(`订单创建测试: ${orderStatus} HTTP ${result.createOrderTest.status}`);
                        if (result.createOrderTest.backendUserName) {
                            console.log(`  使用后台用户: ${result.createOrderTest.backendUserName} (ID: ${result.createOrderTest.backendUserId})`);
                        }
                        if (result.createOrderTest.orderId) {
                            console.log(`  订单ID: ${result.createOrderTest.orderId}`);
                        }
                    }
                }

                if (result.errors.length > 0) {
                    console.log('错误信息:');
                    result.errors.forEach(error => console.log(`  - ${error}`));
                }
            });

            console.log('\n=== 报告结束 ===');
        }

        // 验证API文档合规性
        async function validateAPICompliance() {
            clearDiagnosticResults();
            showDiagnosticResult('=== API文档合规性验证开始 ===');
            showDiagnosticResult('根据"API List to create order.txt"文档进行验证');

            // 1. 验证必填字段
            showDiagnosticResult('\n1. 验证必填字段...');
            const requiredFields = [
                'sub_category_id',
                'car_type_id',
                'incharge_by_backend_user_id',
                'ota_reference_number'
            ];

            showDiagnosticResult(`   必填字段 (${requiredFields.length}个): ${requiredFields.join(', ')}`);

            // 2. 验证可选字段
            showDiagnosticResult('\n2. 验证可选字段...');
            const availableFields = [
                "sub_category_id", "ota", "ota_reference_number", "ota_price",
                "customer_name", "customer_contact", "customer_email", "flight_info",
                "pickup", "pickup_lat", "pickup_long", "date", "time",
                "destination", "destination_lat", "destination_long", "car_type_id",
                "passenger_number", "luggage_number", "driver_fee", "driver_collect",
                "tour_guide", "baby_chair", "meet_and_greet", "extra_requirement",
                "incharge_by_backend_user_id", "driving_region_id", "languages_id_array"
            ];

            showDiagnosticResult(`   可用字段 (${availableFields.length}个): 已验证`);

            // 3. 检查测试用例合规性
            showDiagnosticResult('\n3. 检查测试用例合规性...');
            let complianceIssues = [];

            orderTestCases.forEach((testCase, index) => {
                const data = testCase.data;

                // 检查必填字段
                requiredFields.forEach(field => {
                    if (!data.hasOwnProperty(field) || data[field] === null || data[field] === undefined) {
                        complianceIssues.push(`测试用例${index}: 缺少必填字段 ${field}`);
                    }
                });

                // 检查字段名称
                Object.keys(data).forEach(field => {
                    if (!availableFields.includes(field)) {
                        complianceIssues.push(`测试用例${index}: 未知字段 ${field}`);
                    }
                });

                // 检查languages_id_array格式
                if (data.languages_id_array) {
                    if (!Array.isArray(data.languages_id_array) && typeof data.languages_id_array !== 'object') {
                        complianceIssues.push(`测试用例${index}: languages_id_array格式错误`);
                    }
                }
            });

            if (complianceIssues.length === 0) {
                showDiagnosticResult('   ✅ 所有测试用例符合API文档要求');
            } else {
                showDiagnosticResult(`   ❌ 发现 ${complianceIssues.length} 个合规性问题:`);
                complianceIssues.slice(0, 10).forEach(issue => {
                    showDiagnosticResult(`      - ${issue}`);
                });
                if (complianceIssues.length > 10) {
                    showDiagnosticResult(`      ... 还有 ${complianceIssues.length - 10} 个问题`);
                }
            }

            // 4. 验证API端点
            showDiagnosticResult('\n4. 验证API端点...');
            showDiagnosticResult(`   当前URL: ${API_BASE_URL}/create_order`);
            showDiagnosticResult(`   文档URL: https://gomyhire.com.my/api/create_order`);

            if (`${API_BASE_URL}/create_order` === 'https://gomyhire.com.my/api/create_order') {
                showDiagnosticResult('   ✅ API端点URL正确');
            } else {
                showDiagnosticResult('   ❌ API端点URL不匹配文档');
            }

            // 5. 认证要求分析
            showDiagnosticResult('\n5. 认证要求分析...');
            showDiagnosticResult('   文档第67行: "Create order API does not require login"');
            showDiagnosticResult('   文档第131行: "新api Need login"');
            showDiagnosticResult('   ⚠️  文档存在矛盾，建议测试两种方式');

            // 6. 建议的修复方案
            showDiagnosticResult('\n6. 建议的修复方案...');
            showDiagnosticResult('   1) 使用"测试带认证请求"按钮验证认证是否正常');
            showDiagnosticResult('   2) 检查认证token是否有效');
            showDiagnosticResult('   3) 验证所有测试用例的ID值是否在系统中存在');
            showDiagnosticResult('   4) 如果认证正常但仍有500错误，联系GoMyHire技术支持');

            showDiagnosticResult('\n=== API文档合规性验证完成 ===');
        }

        /**
         * @function initializeAccountSelector - 初始化账号选择器
         * 设置账号选择器的选项和默认值，增强用户体验
         */
        function initializeAccountSelector() {
            const selector = safeGetElement('accountSelector');
            if (!selector) {
                console.warn('未找到账号选择器元素');
                return;
            }

            // 清空现有选项
            selector.innerHTML = '<option value="">选择邮箱账号...</option>';

            // 添加账号选项，包含角色信息
            realLoginAccounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.id;

                // 根据账号类型添加图标和描述
                let accountLabel = account.email;
                if (account.id === 'general') {
                    accountLabel = `📧 ${account.email} (通用账号)`;
                } else if (account.id === 'jcy') {
                    accountLabel = `👤 ${account.email} (操作员)`;
                } else if (account.id === 'sq') {
                    accountLabel = `🔧 ${account.email} (技术支持)`;
                }

                option.textContent = accountLabel;

                if (account.isDefault) {
                    option.selected = true;
                }
                selector.appendChild(option);
            });

            // 初始化状态显示
            updateCurrentAccountInfo('请选择邮箱账号');
            updateSelectedBackendUserInfo(null);

            console.log(`账号选择器初始化完成，共 ${realLoginAccounts.length} 个账号`);
        }

        // 获取当前账号可访问的后台用户列表
        async function loadBackendUsers() {
            if (!authToken) {
                console.warn('未找到认证token，无法获取后台用户列表');
                return [];
            }

            try {
                console.log('正在获取后台用户列表...');
                updateBackendUserInfo('正在加载后台用户列表...');

                const response = await fetch(`${API_BASE_URL}/backend_users?search=`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && Array.isArray(data.data)) {
                        availableBackendUsers = data.data;
                        console.log(`成功获取 ${availableBackendUsers.length} 个后台用户`);

                        // 存储到账号映射中
                        if (currentAccount) {
                            accountBackendUsersMap[currentAccount.id] = availableBackendUsers;
                        }

                        // 更新界面
                        updateBackendUserSelector();

                        // 自动选择第一个用户
                        if (availableBackendUsers.length > 0) {
                            selectBackendUser(availableBackendUsers[0].id);
                        }

                        return availableBackendUsers;
                    } else {
                        console.warn('后台用户API响应格式异常:', data);
                        updateBackendUserInfo('后台用户数据格式异常');
                        return [];
                    }
                } else {
                    const errorText = await response.text();
                    console.warn(`获取后台用户失败: HTTP ${response.status}`, errorText);
                    updateBackendUserInfo(`获取失败: HTTP ${response.status}`);
                    return [];
                }
            } catch (error) {
                console.error('获取后台用户时出错:', error);
                updateBackendUserInfo(`获取失败: ${error.message}`);
                return [];
            }
        }

        /**
         * @function updateBackendUserSelector - 更新后台用户选择器
         * 更新后台用户选择器的选项，包含详细的用户信息
         */
        function updateBackendUserSelector() {
            const selector = safeGetElement('backendUserSelect');
            if (!selector) {
                console.warn('未找到后台用户选择器元素');
                return;
            }

            // 清空现有选项
            selector.innerHTML = '<option value="">选择后台用户...</option>';

            // 按角色排序用户（管理员优先）
            const sortedUsers = [...availableBackendUsers].sort((a, b) => {
                const roleOrder = { 'admin': 1, 'operator': 2, 'branch': 3, 'user': 4 };
                const aOrder = roleOrder[a.role] || 5;
                const bOrder = roleOrder[b.role] || 5;
                return aOrder - bOrder;
            });

            // 添加后台用户选项，包含角色和权限信息
            sortedUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;

                // 根据角色添加图标
                let roleIcon = '👤';
                if (user.role === 'admin') roleIcon = '👑';
                else if (user.role === 'operator') roleIcon = '🔧';
                else if (user.role === 'branch') roleIcon = '🏢';

                // 构建显示文本
                const displayText = `${roleIcon} ${user.name} (ID: ${user.id}) - ${user.role || '未知角色'}`;
                option.textContent = displayText;

                // 为管理员账号添加特殊样式标记
                if (user.role === 'admin') {
                    option.style.fontWeight = 'bold';
                    option.style.color = '#dc3545';
                }

                selector.appendChild(option);
            });

            // 显示后台用户选择器
            const selectorContainer = safeGetElement('backendUserSelector');
            if (selectorContainer) {
                selectorContainer.style.display = 'block';
                // 添加动画效果
                selectorContainer.classList.add('slide-in');
                setTimeout(() => {
                    selectorContainer.classList.remove('slide-in');
                }, 300);
            }

            // 更新信息显示
            const roleStats = availableBackendUsers.reduce((stats, user) => {
                const role = user.role || '未知';
                stats[role] = (stats[role] || 0) + 1;
                return stats;
            }, {});

            const roleStatsText = Object.entries(roleStats)
                .map(([role, count]) => `${role}: ${count}`)
                .join(', ');

            updateBackendUserInfo(`找到 ${availableBackendUsers.length} 个可用后台用户 (${roleStatsText})`);
        }

        /**
         * @function selectBackendUser - 选择后台用户
         * @param {number} userId - 用户ID，如果为null则从选择器获取
         */
        function selectBackendUser(userId = null) {
            // 如果没有指定用户ID，从选择器获取
            if (!userId) {
                const selector = safeGetElement('backendUserSelect');
                if (!selector || !selector.value) return;
                userId = parseInt(selector.value);
            }

            // 查找用户
            const user = availableBackendUsers.find(u => u.id === userId);
            if (!user) {
                console.warn('未找到指定的后台用户:', userId);
                updateSelectedBackendUserInfo(null);
                return;
            }

            // 设置选中的用户
            selectedBackendUser = user;

            // 更新选择器
            const selector = safeGetElement('backendUserSelect');
            if (selector) {
                selector.value = userId;
            }

            // 更新显示信息
            updateSelectedBackendUserInfo(user);

            console.log(`已选择后台用户: ${user.name} (ID: ${user.id}, 角色: ${user.role || '未知'})`);
        }

        // 智能选择后台用户ID
        function getSmartBackendUserId() {
            // 如果有选中的后台用户，使用它
            if (selectedBackendUser) {
                console.log(`使用选中的后台用户: ${selectedBackendUser.name} (ID: ${selectedBackendUser.id})`);
                return selectedBackendUser.id;
            }

            // 如果有可用的后台用户列表，使用第一个
            if (availableBackendUsers && availableBackendUsers.length > 0) {
                console.log(`使用第一个可用后台用户: ${availableBackendUsers[0].name} (ID: ${availableBackendUsers[0].id})`);
                return availableBackendUsers[0].id;
            }

            // 否则使用默认值
            console.log('使用默认后台用户ID: 1');
            return 1;
        }

        // 切换到指定账号
        async function switchToAccount(accountId) {
            const account = realLoginAccounts.find(acc => acc.id === accountId);
            if (!account) {
                console.error('账号不存在:', accountId);
                return;
            }

            // 更新选择器
            const selector = safeGetElement('accountSelector');
            if (selector) {
                selector.value = accountId;
            }

            // 清除之前的认证和后台用户数据
            authToken = null;
            currentAccount = null;
            availableBackendUsers = [];
            selectedBackendUser = null;

            // 隐藏后台用户选择器
            const backendUserSelector = safeGetElement('backendUserSelector');
            if (backendUserSelector) {
                backendUserSelector.style.display = 'none';
            }

            // 开始新账号认证
            updateAuthStatusIndicator(false, `正在切换到 ${account.email}...`);
            updateCurrentAccountInfo('切换中...');
            updateBackendUserInfo('等待账号认证...');

            const success = await authenticateAccount(account);
            if (success) {
                currentAccount = account;
                updateCurrentAccountInfo(`当前邮箱: ${account.email}`);

                // 获取后台用户列表
                await loadBackendUsers();

                // 重新加载系统数据
                await loadAllSystemData();

                console.log(`成功切换到邮箱账号: ${account.email}`);
            } else {
                updateCurrentAccountInfo('认证失败');
                updateBackendUserInfo('认证失败，无法获取后台用户');
            }
        }

        // 从选择器切换账号
        async function switchAccount() {
            const selector = safeGetElement('accountSelector');
            if (!selector || !selector.value) return;

            await switchToAccount(selector.value);
        }

        // 认证指定账号
        async function authenticateAccount(account) {
            try {
                console.log(`正在认证邮箱: ${account.email}`);
                updateAuthStatusIndicator(false, `正在登录 ${account.email}...`);

                const loginResponse = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        'email': account.email,
                        'password': account.password
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    if (loginData.status && loginData.token) {
                        // 提取token（去掉数字|前缀）
                        const fullToken = loginData.token;
                        authToken = fullToken.includes('|') ? fullToken.split('|')[1] : fullToken;

                        console.log(`${account.email} 认证成功`);
                        updateAuthStatusIndicator(true, `✅ ${account.email} 认证成功`);

                        // 记录认证结果
                        if (!accountTestResults[account.id]) {
                            accountTestResults[account.id] = {};
                        }
                        accountTestResults[account.id].authStatus = 'success';
                        accountTestResults[account.id].authTime = new Date().toISOString();
                        accountTestResults[account.id].email = account.email;

                        return true;
                    } else {
                        console.warn(`${account.email} 登录响应中没有token`);
                        updateAuthStatusIndicator(false, `❌ ${account.email} 认证失败：无token`);

                        // 记录认证失败
                        if (!accountTestResults[account.id]) {
                            accountTestResults[account.id] = {};
                        }
                        accountTestResults[account.id].authStatus = 'failed';
                        accountTestResults[account.id].authError = '登录响应中没有token';
                        accountTestResults[account.id].email = account.email;

                        return false;
                    }
                } else {
                    const errorText = await loginResponse.text();
                    console.warn(`${account.email} 登录失败:`, errorText);
                    updateAuthStatusIndicator(false, `❌ ${account.email} 认证失败：${loginResponse.status}`);

                    // 记录认证失败
                    if (!accountTestResults[account.id]) {
                        accountTestResults[account.id] = {};
                    }
                    accountTestResults[account.id].authStatus = 'failed';
                    accountTestResults[account.id].authError = `HTTP ${loginResponse.status}: ${errorText}`;
                    accountTestResults[account.id].email = account.email;

                    return false;
                }

            } catch (error) {
                console.warn(`${account.email} 认证失败:`, error.message);
                updateAuthStatusIndicator(false, `❌ ${account.email} 认证失败：网络错误`);

                // 记录认证失败
                if (!accountTestResults[account.id]) {
                    accountTestResults[account.id] = {};
                }
                accountTestResults[account.id].authStatus = 'failed';
                accountTestResults[account.id].authError = error.message;
                accountTestResults[account.id].email = account.email;

                return false;
            }
        }

        // 自动获取认证token（兼容旧版本）
        async function getAuthToken() {
            // 如果没有当前账号，使用默认账号
            if (!currentAccount) {
                const defaultAccount = testAccounts.find(acc => acc.isDefault);
                if (defaultAccount) {
                    return await authenticateAccount(defaultAccount);
                }
            }

            if (authToken) {
                console.log('已有认证token，跳过获取');
                return authToken;
            }

            // 如果有当前账号但没有token，重新认证
            if (currentAccount) {
                return await authenticateAccount(currentAccount);
            }

            return null;
        }

        /**
         * @function updateAuthStatusIndicator - 更新认证状态指示器
         * @param {boolean} isAuthenticated - 是否已认证
         * @param {string} customMessage - 自定义消息
         */
        function updateAuthStatusIndicator(isAuthenticated, customMessage = null) {
            const indicator = safeGetElement('authStatus');
            const statusIcon = safeGetElement('accountStatusIcon');

            if (indicator) {
                if (isAuthenticated) {
                    indicator.className = 'auth-status auth-success';
                    indicator.style.color = '#155724';
                    indicator.textContent = customMessage || '✅ 认证已完成';
                } else {
                    indicator.className = 'auth-status auth-pending';
                    indicator.style.color = '#856404';
                    indicator.textContent = customMessage || '正在认证中...';
                }
            }

            // 更新账号状态图标
            if (statusIcon) {
                if (isAuthenticated) {
                    statusIcon.className = 'account-status-icon connected';
                } else {
                    statusIcon.className = 'account-status-icon connecting';
                }
            }
        }

        /**
         * @function updateCurrentAccountInfo - 更新当前账号信息显示
         * @param {string} message - 显示消息
         */
        function updateCurrentAccountInfo(message) {
            const infoElement = safeGetElement('currentAccountInfo');
            if (infoElement) {
                infoElement.textContent = message;

                // 添加动画效果
                infoElement.classList.add('slide-in');
                setTimeout(() => {
                    infoElement.classList.remove('slide-in');
                }, 300);
            }
        }

        /**
         * @function updateBackendUserInfo - 更新后台用户信息显示
         * @param {string} message - 显示消息
         */
        function updateBackendUserInfo(message) {
            const infoElement = safeGetElement('backendUserInfo');
            if (infoElement) {
                infoElement.textContent = message;

                // 添加动画效果
                infoElement.classList.add('slide-in');
                setTimeout(() => {
                    infoElement.classList.remove('slide-in');
                }, 300);
            }
        }

        /**
         * @function updateSelectedBackendUserInfo - 更新选中的后台用户信息显示
         * @param {Object} user - 后台用户对象
         */
        function updateSelectedBackendUserInfo(user) {
            const infoElement = safeGetElement('selectedBackendUserInfo');
            if (infoElement && user) {
                infoElement.innerHTML = `
                    <div class="user-name">${user.name}</div>
                    <div class="user-details">
                        <div class="user-detail-item">
                            <span>🆔</span>
                            <span>ID: ${user.id}</span>
                        </div>
                        <div class="user-detail-item">
                            <span>📞</span>
                            <span>${user.phone || '未提供'}</span>
                        </div>
                        <div class="user-detail-item">
                            <span>👤</span>
                            <span>${user.role || '未知角色'}</span>
                        </div>
                    </div>
                `;

                // 添加动画效果
                infoElement.classList.add('slide-in');
                setTimeout(() => {
                    infoElement.classList.remove('slide-in');
                }, 300);
            } else if (infoElement) {
                infoElement.innerHTML = `
                    <div class="user-name">未选择后台用户</div>
                    <div class="user-details">
                        <div class="user-detail-item">
                            <span>🆔</span>
                            <span>请先选择用户</span>
                        </div>
                    </div>
                `;
            }
        }

        // 测试所有邮箱账号
        async function testAllAccounts() {
            console.log('开始测试所有邮箱账号...');
            updateAuthStatusIndicator(false, '正在测试所有邮箱账号...');

            const results = [];

            for (const account of realLoginAccounts) {
                console.log(`\n=== 测试邮箱: ${account.email} ===`);

                const accountResult = {
                    account: account,
                    authSuccess: false,
                    backendUsers: [],
                    selectedBackendUser: null,
                    permissions: {},
                    createOrderTest: null,
                    errors: []
                };

                try {
                    // 1. 测试认证
                    const authSuccess = await authenticateAccount(account);
                    accountResult.authSuccess = authSuccess;

                    if (authSuccess) {
                        currentAccount = account;

                        // 2. 获取后台用户列表
                        const backendUsers = await loadBackendUsers();
                        accountResult.backendUsers = backendUsers;

                        // 3. 选择第一个后台用户
                        if (backendUsers.length > 0) {
                            selectBackendUser(backendUsers[0].id);
                            accountResult.selectedBackendUser = selectedBackendUser;
                        }

                        // 4. 测试API权限
                        await testAccountPermissions(account, accountResult);

                        // 5. 测试订单创建（使用选中的后台用户）
                        await testAccountCreateOrder(account, accountResult);
                    }

                } catch (error) {
                    console.error(`测试邮箱 ${account.email} 时出错:`, error);
                    accountResult.errors.push(error.message);
                }

                results.push(accountResult);

                // 添加延迟避免API限制
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // 显示测试结果
            displayAccountTestResults(results);

            console.log('所有邮箱账号测试完成');
            updateAuthStatusIndicator(true, '所有邮箱账号测试完成');
        }

        // 测试账号权限
        async function testAccountPermissions(account, result) {
            console.log(`测试 ${account.email} 的API权限...`);

            const permissionTests = [
                { name: 'backend_users', url: `${API_BASE_URL}/backend_users?search=` },
                { name: 'car_types', url: `${API_BASE_URL}/car_types?search=` },
                { name: 'sub_categories', url: `${API_BASE_URL}/sub_categories?search=` },
                { name: 'driving_regions', url: `${API_BASE_URL}/driving_regions?search=` },
                { name: 'languages', url: `${API_BASE_URL}/languages?search=` }
            ];

            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };

            for (const test of permissionTests) {
                try {
                    const response = await fetch(test.url, { headers });
                    result.permissions[test.name] = {
                        status: response.status,
                        success: response.ok,
                        hasData: false,
                        dataCount: 0
                    };

                    if (response.ok) {
                        const data = await response.json();
                        result.permissions[test.name].hasData = !!(data.data && data.data.length > 0);
                        result.permissions[test.name].dataCount = data.data ? data.data.length : 0;
                    }

                    console.log(`  ${test.name}: ${response.status} ${response.ok ? '✅' : '❌'}`);

                } catch (error) {
                    result.permissions[test.name] = {
                        status: 0,
                        success: false,
                        error: error.message
                    };
                    console.log(`  ${test.name}: 错误 - ${error.message}`);
                }

                // 添加小延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 测试账号创建订单
        async function testAccountCreateOrder(account, result) {
            console.log(`测试 ${account.email} 的订单创建权限...`);

            // 使用选中的后台用户ID，如果没有则使用默认值1
            const backendUserId = selectedBackendUser ? selectedBackendUser.id : 1;

            const testOrderData = {
                sub_category_id: 2,
                car_type_id: 5,
                incharge_by_backend_user_id: backendUserId,
                ota_reference_number: `TEST_${account.id.toUpperCase()}_${Date.now()}`,
                customer_name: `测试用户_${account.email.split('@')[0]}`,
                customer_contact: '+***********',
                customer_email: `test_${account.id}@example.com`,
                pickup: 'Test Pickup Location',
                destination: 'Test Destination',
                date: '2025-01-15',
                time: '15:30',
                passenger_number: 2,
                luggage_number: 2,
                driving_region_id: 1,
                languages_id_array: [2]
            };

            console.log(`  使用后台用户ID: ${backendUserId} ${selectedBackendUser ? `(${selectedBackendUser.name})` : '(默认)'}`);

            try {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                };

                const response = await fetch(`${API_BASE_URL}/create_order`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(testOrderData)
                });

                const responseText = await response.text();

                result.createOrderTest = {
                    status: response.status,
                    success: response.ok,
                    response: responseText.substring(0, 500),
                    backendUserId: backendUserId,
                    backendUserName: selectedBackendUser ? selectedBackendUser.name : '默认用户'
                };

                if (response.ok) {
                    try {
                        const orderResult = JSON.parse(responseText);
                        result.createOrderTest.orderId = orderResult.order_id || orderResult.data?.order_id;
                        result.createOrderTest.message = orderResult.message;
                    } catch (e) {
                        // 响应不是JSON格式
                    }
                }

                console.log(`  订单创建: ${response.status} ${response.ok ? '✅' : '❌'}`);

            } catch (error) {
                result.createOrderTest = {
                    status: 0,
                    success: false,
                    error: error.message,
                    backendUserId: backendUserId,
                    backendUserName: selectedBackendUser ? selectedBackendUser.name : '默认用户'
                };
                console.log(`  订单创建: 错误 - ${error.message}`);
            }
        }
    </script>
</body>
</html>